defmodule MqttableWeb.ConnectionsLive.SubscriptionManager do
  @moduledoc """
  Manages MQTT subscription operations and state.

  This module handles:
  - Subscription creation and editing
  - Topic subscription and unsubscription
  - Subscription parameter validation and conversion
  - Subscription state management across connection sets

  All functions follow functional programming principles with pattern matching,
  pure functions where possible, and proper error handling with tagged tuples.
  """

  require Logger
  import Phoenix.LiveView, only: [put_flash: 3]
  import Phoenix.Component, only: [assign: 3]

  alias Mqttable.ConnectionSets
  alias Mqttable.MqttClient.Manager, as: MqttClientManager

  # Type definitions
  @type socket :: Phoenix.LiveView.Socket.t()
  @type connection_set :: map()
  @type connection :: map()
  @type connection_sets :: [connection_set()]

  @doc """
  Handles the edit_subscription event.

  Parses subscription parameters, finds the connection and topic data,
  converts values, and sets up the subscription edit modal.
  """
    def handle_edit_subscription(socket, params) do
    with {:ok, parsed_params} <- parse_subscription_params(params),
         {:ok, connection_data} <-
           find_connection_and_topic_data(socket.assigns.connection_sets, parsed_params),
         {:ok, converted_values} <- convert_subscription_values(parsed_params, connection_data) do
      socket = setup_subscription_edit_modal(socket, connection_data, converted_values)
      {:noreply, socket}
    else
      {:error, message} ->
        {:noreply, put_flash(socket, :error, message)}
    end
  end

  @doc """
  Handles the unsubscribe_topic event.

  Unsubscribes from a topic and updates the connection state.
  """
    def handle_unsubscribe_topic(socket, client_id, topic) do
    connection_sets = socket.assigns.connection_sets
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil and connection_index != nil do
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)
      client_status = MqttClientManager.get_status(set.name, client_id)

      case client_status do
        :connected ->
          case MqttClientManager.unsubscribe(set.name, client_id, topic) do
            {:ok, _props, _reason_codes} ->
              {updated_connection_sets, _success_message} =
                remove_topic_from_connection_state(
                  connection_sets,
                  set_index,
                  connection_index,
                  connection,
                  topic
                )

              ConnectionSets.update(updated_connection_sets)
              {:noreply, socket}

            {:error, :not_connected} ->
              {updated_connection_sets, _success_message} =
                remove_topic_from_connection_state(
                  connection_sets,
                  set_index,
                  connection_index,
                  connection,
                  topic
                )

              ConnectionSets.update(updated_connection_sets)
              {:noreply, socket}

            {:error, _reason, error_message} ->
              socket = put_flash(socket, :error, "Failed to unsubscribe: #{error_message}")
              {:noreply, socket}
          end

        _ ->
          {updated_connection_sets, _success_message} =
            remove_topic_from_connection_state(
              connection_sets,
              set_index,
              connection_index,
              connection,
              topic
            )

          ConnectionSets.update(updated_connection_sets)
          {:noreply, socket}
      end
    else
      socket = put_flash(socket, :error, "Connection not found")
      {:noreply, socket}
    end
  end

  @doc """
  Handles subscription to a topic.

  Subscribes to a topic with the specified options and updates the connection state.
  """
    def handle_subscribe_to_topic(socket, client_id, topic, sub_opts, sub_id, index) do
    connection_sets = socket.assigns.connection_sets
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil and connection_index != nil do
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      opts = [sub_opts: sub_opts]
      opts = if sub_id, do: Keyword.put(opts, :id, sub_id), else: opts

      case MqttClientManager.subscribe(set.name, client_id, topic, opts) do
        {:ok, _props, _reason_codes} ->
          updated_connection_sets =
            add_topic_to_connection_state(
              connection_sets,
              set_index,
              connection_index,
              connection,
              topic,
              sub_opts,
              sub_id,
              index
            )

          ConnectionSets.update(updated_connection_sets)
          {:noreply, socket}

        {:error, _reason, error_message} ->
          socket = put_flash(socket, :error, "Failed to subscribe: #{error_message}")
          {:noreply, socket}

        {:error, :not_connected} ->
          socket = put_flash(socket, :error, "Client is not connected")
          {:noreply, socket}
      end
    else
      socket = put_flash(socket, :error, "Connection not found")
      {:noreply, socket}
    end
  end

  @doc """
  Handles MQTT client topic subscribed notifications.

  Updates the connection state when a topic subscription is confirmed.
  """
    def handle_mqtt_client_topic_subscribed(socket, client_id, topic, opts, sub_id) do
    connection_sets = socket.assigns.connection_sets
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil and connection_index != nil do
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      current_topics = connection.topics || []

      topic_exists =
        Enum.any?(current_topics, fn
          %{topic: topic_str} -> topic_str == topic
          _ -> false
        end)

      if not topic_exists do
        updated_connection_sets =
          add_topic_to_connection_state(
            connection_sets,
            set_index,
            connection_index,
            connection,
            topic,
            opts,
            sub_id,
            nil
          )

        ConnectionSets.update(updated_connection_sets)
        socket = update_socket_with_connection_sets(socket, updated_connection_sets)
        {:noreply, socket}
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handles MQTT client topic unsubscribed notifications.

  Updates the connection state when a topic unsubscription is confirmed.
  """
            {:noreply, socket()}
  def handle_mqtt_client_topic_unsubscribed(socket, client_id, topic) do
    connection_sets = socket.assigns.connection_sets
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil and connection_index != nil do
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      {updated_connection_sets, _success_message} =
        remove_topic_from_connection_state(
          connection_sets,
          set_index,
          connection_index,
          connection,
          topic
        )

      ConnectionSets.update(updated_connection_sets)
      socket = update_socket_with_connection_sets(socket, updated_connection_sets)
      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handles subscription failure notifications.

  Removes failed subscriptions from the connection state and shows error messages.
  """
            {:noreply, socket()}
  def handle_mqtt_client_subscription_failed(socket, client_id, topic, error_message) do
    flash_message =
      "Failed to resubscribe to topic '#{topic}' for client '#{client_id}': #{error_message}"

    socket = put_flash(socket, :error, flash_message)

    connection_sets = socket.assigns.connection_sets
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil and connection_index != nil do
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      {updated_connection_sets, _success_message} =
        remove_topic_from_connection_state(
          connection_sets,
          set_index,
          connection_index,
          connection,
          topic
        )

      ConnectionSets.update(updated_connection_sets)
      socket = update_socket_with_connection_sets(socket, updated_connection_sets)
      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  # Private functions

    defp parse_subscription_params(params) do
    required_fields = ["client_id", "topic", "qos", "nl", "rap", "rh"]

    case Enum.find(required_fields, fn field -> is_nil(params[field]) end) do
      nil ->
        {:ok,
         %{
           client_id: params["client_id"],
           topic: params["topic"],
           qos: params["qos"],
           nl: params["nl"],
           rap: params["rap"],
           rh: params["rh"],
           sub_id: params["sub_id"],
           index: params["index"]
         }}

      missing_field ->
        {:error, "Missing required field: #{missing_field}"}
    end
  end

  defp find_connection_and_topic_data(connection_sets, %{
         client_id: client_id,
         topic: topic,
         index: index
       }) do
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index do
      active_set = Enum.at(connection_sets, set_index)

      connection =
        if connection_index, do: Enum.at(active_set.connections, connection_index), else: nil

      {topic_entry, topic_index} = find_topic_entry_and_index(connection, topic, index)

      {:ok,
       %{
         active_set: active_set,
         connection: connection,
         topic_entry: topic_entry,
         topic_index: topic_index
       }}
    else
      {:error, "Connection not found"}
    end
  end

            {map() | nil, integer() | nil}
  defp find_topic_entry_and_index(connection, topic, index) do
    if connection && connection.topics do
      topics = connection.topics

      if index && index != "" do
        find_topic_by_index(topics, index, topic)
      else
        find_topic_by_name(topics, topic)
      end
    else
      {nil, nil}
    end
  end

    defp find_topic_by_index(topics, index, topic) do
    case Integer.parse(index) do
      {idx, _} when idx >= 0 and idx < length(topics) ->
        {Enum.at(topics, idx), idx}

      _ ->
        find_topic_by_name(topics, topic)
    end
  end

    defp find_topic_by_name(topics, topic) do
    idx =
      Enum.find_index(topics, fn
        %{topic: t} -> t == topic
        _ -> false
      end)

    if idx, do: {Enum.at(topics, idx), idx}, else: {nil, nil}
  end

    defp convert_subscription_values(params, %{topic_entry: topic_entry}) do
    sub_id_value = extract_subscription_id(params.sub_id, topic_entry)

    {:ok,
     %{
       client_id: params.client_id,
       topic: params.topic,
       qos: convert_to_integer(params.qos, 0),
       nl: convert_to_boolean_int(params.nl),
       rap: convert_to_boolean_int(params.rap),
       rh: convert_to_integer(params.rh, 0),
       sub_id: sub_id_value,
       index: params.index
     }}
  end

    defp extract_subscription_id(sub_id, topic_entry) do
    cond do
      sub_id && sub_id != "" ->
        sub_id

      topic_entry && is_map(topic_entry) && Map.has_key?(topic_entry, :id) ->
        id = Map.get(topic_entry, :id)
        if is_integer(id) && id > 0, do: Integer.to_string(id), else: ""

      true ->
        ""
    end
  end

    defp convert_to_integer(value, default) do
    case value do
      value when is_binary(value) -> String.to_integer(value)
      value when is_integer(value) -> value
      _ -> default
    end
  end

    defp convert_to_boolean_int(value) do
    case value do
      "true" -> 1
      true -> 1
      1 -> 1
      "1" -> 1
      _ -> 0
    end
  end

    defp setup_subscription_edit_modal(
         socket,
         %{active_set: active_set, topic_index: topic_index},
         values
       ) do
    index_string = if topic_index, do: Integer.to_string(topic_index), else: ""

    socket
    |> assign(:active_connection_set, active_set)
    |> assign(:show_subscription_modal, true)
    |> assign(:edit_mode, true)
    |> assign(:client_id, values.client_id)
    |> assign(:topic, values.topic)
    |> assign(:qos, values.qos)
    |> assign(:nl, values.nl)
    |> assign(:rap, values.rap)
    |> assign(:rh, values.rh)
    |> assign(:sub_id, values.sub_id)
    |> assign(:index, index_string)
  end

  # Helper functions that need to be implemented or imported
  defp find_connection_indices(connection_sets, client_id) do
    # This function should be moved to a shared utility module
    # For now, we'll implement it here
    set_index =
      Enum.find_index(connection_sets, fn set ->
        Enum.any?(set.connections, fn conn -> conn.client_id == client_id end)
      end)

    if set_index do
      set = Enum.at(connection_sets, set_index)

      connection_index =
        Enum.find_index(set.connections, fn conn -> conn.client_id == client_id end)

      {set_index, connection_index}
    else
      {nil, nil}
    end
  end

  defp remove_topic_from_connection_state(
         connection_sets,
         set_index,
         connection_index,
         connection,
         topic
       ) do
    # Implementation for removing topic from connection state
    current_topics = connection.topics || []

    updated_topics =
      Enum.reject(current_topics, fn t ->
        case t do
          %{topic: topic_str} -> topic_str == topic
          _ -> false
        end
      end)

    updated_connection = Map.put(connection, :topics, updated_topics)
    set = Enum.at(connection_sets, set_index)
    updated_connections = List.replace_at(set.connections, connection_index, updated_connection)
    updated_set = Map.put(set, :connections, updated_connections)
    updated_connection_sets = List.replace_at(connection_sets, set_index, updated_set)

    success_message = "Unsubscribed from topic: #{topic}"
    {updated_connection_sets, success_message}
  end

  defp add_topic_to_connection_state(
         connection_sets,
         set_index,
         connection_index,
         connection,
         topic,
         sub_opts,
         sub_id,
         index
       ) do
    # Implementation for adding topic to connection state
    nl_value = Keyword.get(sub_opts, :nl, 0)
    rap_value = Keyword.get(sub_opts, :rap, 0)

    topic_entry = %{
      topic: topic,
      qos: Keyword.get(sub_opts, :qos, 0),
      nl: nl_value,
      rap: rap_value,
      rh: Keyword.get(sub_opts, :rh, 0)
    }

    topic_entry =
      if sub_id && is_integer(sub_id) && sub_id > 0 do
        Map.put(topic_entry, :id, sub_id)
      else
        topic_entry
      end

    current_topics = connection.topics || []

    updated_topics =
      if index && index != "" do
        case Integer.parse(index) do
          {idx, _} when idx >= 0 and idx < length(current_topics) ->
            List.replace_at(current_topics, idx, topic_entry)

          _ ->
            current_topics ++ [topic_entry]
        end
      else
        current_topics ++ [topic_entry]
      end

    updated_connection = Map.put(connection, :topics, updated_topics)
    set = Enum.at(connection_sets, set_index)
    updated_connections = List.replace_at(set.connections, connection_index, updated_connection)
    updated_set = Map.put(set, :connections, updated_connections)
    List.replace_at(connection_sets, set_index, updated_set)
  end

  defp update_socket_with_connection_sets(socket, updated_connection_sets) do
    # Update socket assigns with new connection sets
    active_set_name =
      if socket.assigns.active_connection_set,
        do: socket.assigns.active_connection_set.name,
        else: nil

    updated_active_set =
      if active_set_name do
        Enum.find(updated_connection_sets, fn set -> set.name == active_set_name end)
      else
        nil
      end

    socket
    |> assign(:connection_sets, updated_connection_sets)
    |> assign(:active_connection_set, updated_active_set)
  end
end
