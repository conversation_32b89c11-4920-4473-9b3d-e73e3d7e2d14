defmodule MqttableWeb.ConnectionsLive.FileUploadHandler do
  @moduledoc """
  Handles file upload operations for the connections LiveView.

  This module handles:
  - File upload processing and validation
  - Base64 encoding and decoding
  - File storage and retrieval
  - Upload error handling and user feedback
  - Integration with send and scheduled message modals

  All functions follow functional programming principles with pattern matching,
  pure functions where possible, and proper error handling with tagged tuples.
  """

  require Logger
  import Phoenix.LiveView, only: [send_update: 2]
  alias Mqttable.Uploads.FileStorage

  # Type definitions
  @type socket :: Phoenix.LiveView.Socket.t()
  @type upload_result :: {:ok, map()} | {:error, String.t()}

  @doc """
  Handles file upload events from the client.

  Processes uploaded files, stores them, and notifies the appropriate modal components.
  """
            {:noreply, socket()}
  def handle_file_uploaded(socket, filename, base64_content, size, type) do
    Logger.info("ConnectionsLive: File upload received: #{filename} (#{size} bytes)")

    case process_uploaded_file(filename, base64_content, size, type) do
      {:ok, file_data} ->
        notify_modal_components(socket, file_data)
        {:noreply, socket}

      {:error, error_message} ->
        notify_modal_components_error(socket, error_message)
        {:noreply, socket}
    end
  end

  @doc """
  Handles upload cancellation events.
  """
    def handle_cancel_upload(socket, upload_name, ref) do
    {:noreply, Phoenix.LiveView.cancel_upload(socket, String.to_existing_atom(upload_name), ref)}
  end

  @doc """
  Handles payload changes from file components.
  """
    def handle_payload_changed(socket, _payload) do
    # Handle payload changes from UnifiedPayloadEditorComponent
    # This is typically called when a file is removed or payload is cleared
    # For now, we just acknowledge the message without taking action
    # The component handles its own state updates
    {:noreply, socket}
  end

  # Private functions

    defp process_uploaded_file(filename, base64_content, size, type) do
    with {:ok, file_content} <- decode_base64_content(base64_content),
         {:ok, stored_filename} <- store_file(file_content, filename) do
      Logger.info("File stored successfully: #{stored_filename}")

      {:ok,
       %{
         filename: filename,
         stored_filename: stored_filename,
         size: size,
         type: type
       }}
    else
      {:error, reason} ->
        Logger.error("Failed to process uploaded file: #{reason}")
        {:error, reason}
    end
  end

    defp decode_base64_content(base64_content) do
    case Base.decode64(base64_content) do
      {:ok, file_content} ->
        {:ok, file_content}

      :error ->
        {:error, "Failed to decode file content"}
    end
  end

    defp store_file(file_content, filename) do
    case FileStorage.store_file(file_content, filename) do
      {:ok, stored_filename} ->
        {:ok, stored_filename}

      {:error, error_message} ->
        {:error, error_message}
    end
  end

    defp notify_modal_components(socket, file_data) do
    cond do
      socket.assigns[:show_send_modal] ->
        send_update(MqttableWeb.SendMessageModalComponent,
          id: "send-message-modal",
          file_uploaded: file_data
        )

      socket.assigns[:show_scheduled_message_modal] ->
        send_update(MqttableWeb.ScheduledMessageModalComponent,
          id: "scheduled-message-modal",
          file_uploaded: file_data
        )

      true ->
        Logger.warning("File uploaded but no modal is open to receive it")
    end

    :ok
  end

    defp notify_modal_components_error(socket, error_message) do
    cond do
      socket.assigns[:show_send_modal] ->
        send_update(MqttableWeb.SendMessageModalComponent,
          id: "send-message-modal",
          file_upload_error: error_message
        )

      socket.assigns[:show_scheduled_message_modal] ->
        send_update(MqttableWeb.ScheduledMessageModalComponent,
          id: "scheduled-message-modal",
          file_upload_error: error_message
        )

      true ->
        Logger.warning("File upload error but no modal is open to receive it")
    end

    :ok
  end

  @doc """
  Validates file upload parameters.

  Ensures all required parameters are present and valid.
  """
    def validate_upload_params(params) do
    required_fields = ["filename", "content", "size", "type"]

    case Enum.find(required_fields, fn field -> is_nil(params[field]) end) do
      nil ->
        case validate_file_size(params["size"]) do
          :ok ->
            {:ok,
             %{
               filename: params["filename"],
               content: params["content"],
               size: params["size"],
               type: params["type"]
             }}

          {:error, reason} ->
            {:error, reason}
        end

      missing_field ->
        {:error, "Missing required field: #{missing_field}"}
    end
  end

    defp validate_file_size(size) when is_integer(size) do
    # 10MB limit
    max_size = 10 * 1024 * 1024

    if size > 0 and size <= max_size do
      :ok
    else
      {:error, "File size must be between 1 byte and 10MB"}
    end
  end

  defp validate_file_size(_), do: {:error, "Invalid file size"}

  @doc """
  Formats file size for display.

  Converts bytes to human-readable format (KB, MB, etc.).
  """
    def format_file_size(size) when is_integer(size) and size >= 0 do
    cond do
      size < 1024 ->
        "#{size} B"

      size < 1024 * 1024 ->
        "#{Float.round(size / 1024, 1)} KB"

      size < 1024 * 1024 * 1024 ->
        "#{Float.round(size / (1024 * 1024), 1)} MB"

      true ->
        "#{Float.round(size / (1024 * 1024 * 1024), 1)} GB"
    end
  end

  def format_file_size(_), do: "Unknown size"

  @doc """
  Determines if a file type is supported for upload.

  Returns true if the file type is allowed, false otherwise.
  """
    def is_supported_file_type?(type) when is_binary(type) do
    supported_types = [
      # Text files
      "text/plain",
      "text/csv",
      "text/xml",
      "application/json",
      "application/xml",

      # Image files
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "image/svg+xml",

      # Document files
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",

      # Archive files
      "application/zip",
      "application/x-tar",
      "application/gzip",

      # Binary files
      "application/octet-stream"

    type in supported_types
  end

  def is_supported_file_type?(_), do: false

  @doc """
  Extracts file extension from filename.

  Returns the file extension in lowercase, or empty string if no extension.
  """
    def get_file_extension(filename) when is_binary(filename) do
    case Path.extname(filename) do
      "." <> ext -> String.downcase(ext)
      _ -> ""
    end
  end

  def get_file_extension(_), do: ""

  @doc """
  Generates a safe filename for storage.

  Sanitizes the filename to prevent security issues and ensure compatibility.
  """
    def sanitize_filename(filename) when is_binary(filename) do
    # Remove or replace unsafe characters
    filename
    |> String.replace(~r/[^\w\-_\.]/, "_")
    |> String.replace(~r/_+/, "_")
    |> String.trim("_")
    |> case do
      "" -> "unnamed_file"
      sanitized -> sanitized
    end
  end

  def sanitize_filename(_), do: "unnamed_file"
end
