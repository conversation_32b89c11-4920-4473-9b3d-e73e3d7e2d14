defmodule MqttableWeb.ConnectionsLive.ConnectionManager do
  @moduledoc """
  Manages MQTT connection operations and status updates.

  This module handles:
  - MQTT connection establishment and disconnection
  - Connection status updates and validation
  - Connection error handling and recovery
  - Connection state synchronization across the application

  All functions follow functional programming principles with pattern matching,
  pure functions where possible, and proper error handling with tagged tuples.
  """

  require Logger
  import Phoenix.Component, only: [assign: 3]

  alias Mqttable.ConnectionSets
  alias Mqttable.MqttClient.Manager, as: <PERSON><PERSON>tt<PERSON>lient<PERSON>ana<PERSON>
  alias MqttableWeb.Utils.ConnectionHelpers

  # Type definitions
  @type socket :: Phoenix.LiveView.Socket.t()
  @type connection_set :: map()
  @type connection :: map()
  @type connection_sets :: [connection_set()]
  @type mqtt_result :: {:ok, any()} | {:error, atom(), String.t()}

  @doc """
  Handles the update_connection_status event.

  Updates the MQTT connection status for a specific client, handling connection
  establishment, disconnection, and error scenarios.
  """
  @spec handle_update_connection_status(socket(), String.t(), String.t()) :: {:noreply, socket()}
  def handle_update_connection_status(socket, client_id, new_status) do
    with {:ok, active_set} <- validate_active_connection_set(socket),
         {:ok, connection} <- find_connection_in_set(active_set, client_id),
         {:ok, actual_status} <-
           attempt_mqtt_connection_change(connection, active_set, client_id, new_status) do
      updated_connection_sets =
        update_connection_status_in_sets(
          socket.assigns.connection_sets,
          active_set,
          client_id,
          actual_status
        )

      socket = update_connection_sets_and_socket(socket, updated_connection_sets)
      {:noreply, socket}
    else
      {:error, _reason} ->
        {:noreply, socket}
    end
  end

  def handle_mqtt_client_status_changed(socket, broker_name, client_id, status) do
    connection_sets = socket.assigns.connection_sets
    my_set = Enum.find(connection_sets, fn set -> set.name == broker_name end)

    if my_set do
      connections = Map.get(my_set, :connections, [])
      connection = Enum.find(connections, fn conn -> conn.client_id == client_id end)

      if connection do
        string_status = Atom.to_string(status)

        if connection.status != string_status do
          updated_connection_sets =
            update_connection_status_in_sets(
              socket.assigns.connection_sets,
              my_set,
              client_id,
              string_status
            )

          socket = update_connection_sets_and_socket(socket, updated_connection_sets)
          {:noreply, socket}
        else
          {:noreply, socket}
        end
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handles MQTT client connection time updates.

  Updates the connection timestamp when a client successfully connects.
  """
  @spec handle_mqtt_client_connection_time(socket(), String.t(), any()) :: {:noreply, socket()}
  def handle_mqtt_client_connection_time(socket, client_id, timestamp) do
    active_set = socket.assigns.active_connection_set

    if active_set do
      connections = Map.get(active_set, :connections, [])
      connection = Enum.find(connections, fn conn -> conn.client_id == client_id end)

      if connection do
        updated_connection_sets =
          update_connection_in_sets(
            socket.assigns.connection_sets,
            client_id,
            fn conn -> Map.put(conn, :connection_time, timestamp) end
          )

        socket = update_connection_sets_and_socket(socket, updated_connection_sets)
        {:noreply, socket}
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  # Private functions

  @spec validate_active_connection_set(socket()) ::
          {:ok, connection_set()} | {:error, :no_active_set}
  defp validate_active_connection_set(socket) do
    case socket.assigns.active_connection_set do
      nil -> {:error, :no_active_set}
      active_set -> {:ok, active_set}
    end
  end

  @spec find_connection_in_set(connection_set(), String.t()) ::
          {:ok, connection()} | {:error, :connection_not_found}
  defp find_connection_in_set(active_set, client_id) do
    connections = Map.get(active_set, :connections, [])

    case Enum.find(connections, fn conn -> conn.client_id == client_id end) do
      nil -> {:error, :connection_not_found}
      connection -> {:ok, connection}
    end
  end

  @spec attempt_mqtt_connection_change(connection(), connection_set(), String.t(), String.t()) ::
          {:ok, String.t()}
  defp attempt_mqtt_connection_change(connection, active_set, client_id, new_status) do
    connection_result = perform_mqtt_operation(connection, active_set, client_id, new_status)

    case connection_result do
      {:ok, status} -> {:ok, status}
      {:error, status, _message} -> {:ok, status}
    end
  end

  @spec perform_mqtt_operation(connection(), connection_set(), String.t(), String.t()) ::
          mqtt_result()
  defp perform_mqtt_operation(connection, active_set, client_id, new_status) do
    try do
      execute_mqtt_operation(connection, active_set, client_id, new_status)
    catch
      :exit, {:shutdown, :tcp_closed} ->
        handle_connection_error(
          client_id,
          "Connection closed by broker before completing handshake"
        )

      :exit, {:socket_closed_before_connack, _} ->
        handle_connection_error(
          client_id,
          "Connection closed by broker before completing handshake"
        )

      :exit, reason ->
        error_message = extract_concise_error_message(reason)
        handle_connection_error(client_id, error_message)
    end
  end

  @spec execute_mqtt_operation(connection(), connection_set(), String.t(), String.t()) ::
          mqtt_result()
  defp execute_mqtt_operation(connection, active_set, client_id, new_status) do
    case new_status do
      "connected" ->
        case MqttClientManager.connect(connection, active_set) do
          {:ok, _} ->
            {:ok, new_status}

          {:error, reason, error_message} ->
            Logger.error(
              "Failed to connect MQTT client #{client_id}: #{inspect(reason)} - #{error_message}"
            )

            {:error, "reconnecting", error_message}
        end

      "disconnected" ->
        MqttClientManager.disconnect(active_set.name, client_id)
        {:ok, new_status}

      _ ->
        {:ok, new_status}
    end
  end

  @spec handle_connection_error(String.t(), String.t()) :: {:error, String.t(), String.t()}
  defp handle_connection_error(client_id, error_message) do
    Logger.error("MQTT client #{client_id}: #{error_message}")
    {:error, "reconnecting", error_message}
  end

  @spec update_connection_status_in_sets(
          connection_sets(),
          connection_set(),
          String.t(),
          String.t()
        ) :: connection_sets()
  defp update_connection_status_in_sets(connection_sets, active_set, client_id, actual_status) do
    updated_connections =
      update_connections_with_status(active_set.connections, client_id, actual_status)

    updated_set = Map.put(active_set, :connections, updated_connections)

    Enum.map(connection_sets, fn set ->
      if set.name == active_set.name, do: updated_set, else: set
    end)
  end

  @spec update_connections_with_status(list(), String.t(), String.t()) :: list()
  defp update_connections_with_status(connections, client_id, actual_status) do
    Enum.map(connections, fn conn ->
      if conn.client_id == client_id do
        update_connection_with_status(conn, actual_status)
      else
        conn
      end
    end)
  end

  @spec update_connection_with_status(connection(), String.t()) :: connection()
  defp update_connection_with_status(conn, actual_status) do
    if actual_status == "disconnected" do
      conn
      |> Map.put(:status, actual_status)
      |> Map.put(:connection_time, nil)
    else
      Map.put(conn, :status, actual_status)
    end
  end

  @spec update_connection_sets_and_socket(socket(), connection_sets()) :: socket()
  defp update_connection_sets_and_socket(socket, updated_connection_sets) do
    # Update the connection sets in the server
    ConnectionSets.update(updated_connection_sets)

    # Update socket assigns
    active_set_name =
      if socket.assigns.active_connection_set,
        do: socket.assigns.active_connection_set.name,
        else: nil

    updated_active_set =
      if active_set_name,
        do:
          ConnectionHelpers.find_connection_set_by_name(updated_connection_sets, active_set_name),
        else: nil

    socket
    |> assign(:connection_sets, updated_connection_sets)
    |> assign(:active_connection_set, updated_active_set)
  end

  @spec update_connection_in_sets(connection_sets(), String.t(), (connection() -> connection())) ::
          connection_sets()
  defp update_connection_in_sets(connection_sets, client_id, update_fn) do
    Enum.map(connection_sets, fn connection_set ->
      updated_connections =
        Enum.map(connection_set.connections, fn connection ->
          if connection.client_id == client_id do
            update_fn.(connection)
          else
            connection
          end
        end)

      Map.put(connection_set, :connections, updated_connections)
    end)
  end

  @spec extract_concise_error_message(any()) :: String.t()
  defp extract_concise_error_message(reason) do
    cond do
      is_tuple(reason) && tuple_size(reason) >= 2 &&
          elem(reason, 0) == :socket_closed_before_connack ->
        "Connection closed by broker before completing handshake"

      is_tuple(reason) && tuple_size(reason) >= 2 && elem(reason, 0) == :shutdown &&
          elem(reason, 1) == :tcp_closed ->
        "Connection closed by broker before completing handshake"

      is_tuple(reason) && tuple_size(reason) >= 2 && is_tuple(elem(reason, 0)) ->
        inner_reason = elem(reason, 0)

        if is_tuple(inner_reason) && tuple_size(inner_reason) >= 2 do
          case elem(inner_reason, 0) do
            :shutdown ->
              case elem(inner_reason, 1) do
                :tcp_closed -> "Connection closed by broker before completing handshake"
                other -> "Connection error: #{inspect(other)}"
              end

            other ->
              "Connection error: #{inspect(other)}"
          end
        else
          "Connection error: #{inspect(inner_reason)}"
        end

      true ->
        "Connection error: #{inspect(reason)}"
    end
  end
end
