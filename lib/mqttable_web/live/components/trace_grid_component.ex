defmodule MqttableWeb.TraceGridComponent do
  @moduledoc """
  LiveComponent for displaying MQTT trace data using SlickGrid.
  This component provides high-performance rendering of large datasets with client-side
  filtering, sorting, and pagination handled entirely by SlickGrid.
  """
  use MqttableWeb, :live_component
  require Logger

  @impl true
  def update(assigns, socket) do
    # Ensure we have a valid active_broker_name
    active_broker_name = assigns[:active_broker_name]

    # Check if the active broker has changed
    broker_changed =
      Map.has_key?(assigns, :active_broker_name) &&
        socket.assigns[:active_broker_name] != active_broker_name

    # Load filter state for the current broker
    filter_state = load_filter_state(active_broker_name)

    socket =
      socket
      |> assign(assigns)
      |> assign_new(:selected_message, fn -> nil end)
      |> assign_new(:grid_data, fn -> [] end)
      # Filter state assigns
      |> assign(:topic_filter, filter_state.topic_filter)
      |> assign(:payload_filter, filter_state.payload_filter)
      |> assign(:selected_client_ids, filter_state.selected_client_ids)
      |> assign(:ignore_ping_packets, filter_state.ignore_ping_packets)
      |> assign(:topic_grouping_enabled, filter_state[:topic_grouping_enabled] || false)
      |> assign(:available_client_ids, get_broker_client_ids(active_broker_name))

    # Initialize or update the grid data
    socket =
      cond do
        # Broker changed - completely reinitialize
        broker_changed ->
          Logger.debug("Broker changed, reinitializing data")
          # Reload filter state for the new broker
          new_filter_state = load_filter_state(active_broker_name)
          new_client_ids = get_broker_client_ids(active_broker_name)

          socket =
            socket
            |> assign(:selected_message, nil)
            # Update filter state for new broker
            |> assign(:topic_filter, new_filter_state.topic_filter)
            |> assign(:payload_filter, new_filter_state.payload_filter)
            |> assign(:selected_client_ids, new_filter_state.selected_client_ids)
            |> assign(:ignore_ping_packets, new_filter_state.ignore_ping_packets)
            |> assign(:topic_grouping_enabled, new_filter_state[:topic_grouping_enabled] || false)
            |> assign(:available_client_ids, new_client_ids)
            |> load_all_data()

          # Push data update to JavaScript
          push_grid_data_update(socket)
          socket

        # First time loading or grid_data is empty - initialize
        not Map.has_key?(socket.assigns, :grid_data) or socket.assigns[:grid_data] == [] ->
          Logger.debug("First time loading or empty grid_data, initializing")
          socket = load_all_data(socket)
          # Push data update to JavaScript
          push_grid_data_update(socket)
          socket

        # Stream data will be handled by the JavaScript hook monitoring the hidden stream container
        # We don't need to process it here since the hook will detect DOM changes
        true ->
          socket
      end

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="flex-grow flex flex-col overflow-hidden gap-2 main-content-area relative">
      <!-- trace section -->
      <div class="w-full flex flex-col bg-base-100 p-2">
        <!-- Filter controls - ultra-compact single row layout -->
        <div class="mb-4">
          <div class="flex flex-wrap gap-2 items-center text-sm">
            <!-- Message/Topic count -->
            <div class="badge badge-info badge-outline">
              <.icon name="hero-inbox" class="size-3 mr-1" />
              <span id="message-count-display" class="font-mono text-xs">
                <%= if @topic_grouping_enabled do %>
                  {count_unique_topics(@grid_data)} topics
                <% else %>
                  {length(@grid_data)} messages
                <% end %>
              </span>
            </div>
            
    <!-- Topic View toggle -->
            <label class="label cursor-pointer p-0">
              <input
                type="checkbox"
                class="toggle toggle-primary toggle-xs"
                phx-click="toggle_topic_grouping"
                phx-target={@myself}
                checked={@topic_grouping_enabled}
              />
              <span class="label-text ml-1 text-xs">Topic View</span>
            </label>
            
    <!-- Client ID filter dropdown -->
            <div class="dropdown dropdown-bottom">
              <div tabindex="0" role="button" class="btn btn-xs btn-outline">
                <img src="/images/device.svg" alt="Client" class="w-3 h-3 mr-1" />
                <span class="text-xs">
                  {if Enum.empty?(@selected_client_ids) or
                        length(@selected_client_ids) == length(@available_client_ids),
                      do: "All Clients",
                      else: "#{length(@selected_client_ids)} Selected"}
                </span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </div>
              <div
                tabindex="0"
                class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-64 max-h-60 overflow-auto"
              >
                <div class="form-control">
                  <label class="label cursor-pointer justify-start">
                    <input
                      type="checkbox"
                      class="checkbox checkbox-xs"
                      phx-click="toggle_all_clients"
                      phx-target={@myself}
                      checked={
                        Enum.empty?(@selected_client_ids) or
                          length(@selected_client_ids) == length(@available_client_ids)
                      }
                    />
                    <img src="/images/device.svg" alt="Client" class="w-3 h-3 mr-1" />
                    <span class="label-text ml-1 text-xs">All Clients</span>
                  </label>
                  <%= for client_id <- @available_client_ids do %>
                    <label class="label cursor-pointer justify-start">
                      <input
                        type="checkbox"
                        class="checkbox checkbox-xs"
                        phx-click="toggle_client_id"
                        phx-value-client_id={client_id}
                        phx-target={@myself}
                        checked={client_id in @selected_client_ids}
                      />
                      <span class="label-text ml-2 font-mono text-xs">{client_id}</span>
                    </label>
                  <% end %>
                </div>
              </div>
            </div>
            
    <!-- Filter inputs container - flex-grow to fill available space -->
            <div class="flex flex-1 gap-2 min-w-0">
              <!-- Topic filter -->
              <input
                type="text"
                placeholder="Topic filter..."
                value={@topic_filter}
                class="input input-xs input-bordered flex-1 min-w-[120px]"
                phx-keyup="topic_filter_changed"
                phx-target={@myself}
              />
              
    <!-- Payload filter -->
              <input
                type="text"
                placeholder="Payload filter..."
                value={@payload_filter}
                class="input input-xs input-bordered flex-1 min-w-[120px]"
                phx-keyup="payload_filter_changed"
                phx-target={@myself}
              />
            </div>
            
    <!-- Ignore PING option -->
            <label class="label cursor-pointer p-0">
              <input
                type="checkbox"
                class="checkbox checkbox-xs"
                phx-click="toggle_ignore_ping"
                phx-target={@myself}
                checked={@ignore_ping_packets}
              />
              <span class="label-text ml-1 text-xs">Ignore PING</span>
            </label>
            
    <!-- Export dropdown -->
            <div class="dropdown dropdown-end">
              <div tabindex="0" role="button" class="btn btn-xs btn-outline btn-accent">
                <.icon name="hero-arrow-down-tray" class="size-3 mr-1" />
                <span class="text-xs">Export</span>
              </div>
              <ul
                tabindex="0"
                class="dropdown-content menu bg-base-100 rounded-box z-[1] w-48 p-2 shadow"
              >
                <li>
                  <a
                    phx-click="export_csv"
                    phx-target={@myself}
                    href="javascript:void(0)"
                    class="text-xs"
                  >
                    <.icon name="hero-document-text" class="size-3" /> CSV
                  </a>
                </li>
                <li>
                  <a
                    phx-click="export_excel"
                    phx-target={@myself}
                    href="javascript:void(0)"
                    class="text-xs"
                  >
                    <.icon name="hero-table-cells" class="size-3" /> Excel
                  </a>
                </li>
                <li>
                  <a
                    phx-click="export_json"
                    phx-target={@myself}
                    href="javascript:void(0)"
                    class="text-xs"
                  >
                    <.icon name="hero-code-bracket" class="size-3" /> JSON
                  </a>
                </li>
              </ul>
            </div>
            
    <!-- Delete button -->
            <button
              class="btn btn-xs btn-outline btn-error"
              phx-click="clear_trace"
              phx-target={@myself}
            >
              <.icon name="hero-trash" class="size-3 mr-1" />
              <span class="text-xs">Delete</span>
            </button>
          </div>
        </div>
        
    <!-- SlickGrid container -->
        <div class="trace-grid-container">
          <div
            id={generate_grid_id(@active_broker_name)}
            class="trace-slick-grid"
            phx-hook="TraceSlickGrid"
            phx-target={@myself}
            phx-update="ignore"
            data-grid-data={Jason.encode!(serialize_grid_data(@grid_data))}
            data-topic-filter={@topic_filter}
            data-payload-filter={@payload_filter}
            data-selected-client-ids={Jason.encode!(@selected_client_ids)}
            data-ignore-ping-packets={to_string(@ignore_ping_packets)}
            data-topic-grouping-enabled={to_string(@topic_grouping_enabled)}
            data-time-zone={Mqttable.Settings.get_time_zone()}
            data-broker-name={@active_broker_name || ""}
          >
          </div>
        </div>
      </div>
    </div>
    """
  end

  # Event handlers

  @impl true
  def handle_event("select_message", %{"id" => id}, socket) do
    # Find the selected message by ID
    # Handle both string and integer IDs from JavaScript
    id =
      case id do
        id when is_integer(id) -> id
        id when is_binary(id) -> String.to_integer(id)
      end

    # Always search for the message directly in TraceManager for the most up-to-date data
    # This avoids synchronization issues between grid_data and stream updates
    broker_name = socket.assigns[:active_broker_name]

    selected_message =
      if broker_name do
        Mqttable.TraceManager.get_message_by_id(broker_name, id)
      else
        Logger.debug("No broker_name available for message selection")
        nil
      end

    # Serialize the message to ensure it's JSON-safe before sending to parent LiveView
    # Use full serialization for detail modal (no payload truncation)
    serialized_message =
      if selected_message, do: serialize_message_for_detail(selected_message), else: nil

    # Send serialized message to parent LiveView to open detail modal
    send(self(), {:open_detail_modal, serialized_message})

    # Keep for backward compatibility
    socket = assign(socket, :selected_message, selected_message)

    {:noreply, socket}
  end

  @impl true
  def handle_event("clear_trace", _params, socket) do
    # Get the active broker name from the parent assigns
    broker_name = socket.assigns[:active_broker_name]
    # Clear the trace messages from ETS table if broker name is available
    if broker_name do
      Mqttable.TraceManager.clear_messages(broker_name)
    end

    # Reset filter state to defaults
    default_state = default_filter_state()

    # Clear the grid data and reset state
    socket =
      socket
      |> assign(:grid_data, [])
      |> assign(:selected_message, nil)
      |> assign(:loading_older, false)
      |> assign(:loading_newer, false)
      |> assign(:oldest_key, nil)
      |> assign(:newest_key, nil)
      |> assign(:has_more_before, true)
      |> assign(:has_more_after, false)
      # Reset filter state
      |> assign(:topic_filter, default_state.topic_filter)
      |> assign(:payload_filter, default_state.payload_filter)
      |> assign(:selected_client_ids, default_state.selected_client_ids)
      |> assign(:ignore_ping_packets, default_state.ignore_ping_packets)
      |> assign(:topic_grouping_enabled, default_state[:topic_grouping_enabled] || false)

    # Save the reset filter state
    save_filter_state(socket)

    # Push empty grid data to frontend to clear the SlickGrid table
    push_grid_data_update(socket)

    # Send message to parent LiveView to clear its trace_messages state
    send(self(), {:clear_trace_messages, broker_name})

    {:noreply, socket}
  end

  @impl true
  def handle_event("topic_filter_changed", %{"value" => value}, socket) do
    socket = assign(socket, :topic_filter, value)
    save_filter_state(socket)
    # Push filter update to JavaScript
    push_filter_update(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("payload_filter_changed", %{"value" => value}, socket) do
    socket = assign(socket, :payload_filter, value)
    save_filter_state(socket)
    # Push filter update to JavaScript
    push_filter_update(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_all_clients", _params, socket) do
    # If all clients are currently selected or none selected, toggle to select none
    # Otherwise, select all clients
    selected_client_ids =
      if Enum.empty?(socket.assigns.selected_client_ids) or
           length(socket.assigns.selected_client_ids) ==
             length(socket.assigns.available_client_ids) do
        []
      else
        socket.assigns.available_client_ids
      end

    socket = assign(socket, :selected_client_ids, selected_client_ids)
    save_filter_state(socket)
    # Push filter update to JavaScript
    push_filter_update(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_client_id", %{"client_id" => client_id}, socket) do
    selected_client_ids = socket.assigns.selected_client_ids

    # Toggle the client ID in the selected client IDs list
    updated_client_ids =
      if client_id in selected_client_ids do
        List.delete(selected_client_ids, client_id)
      else
        [client_id | selected_client_ids]
      end

    socket = assign(socket, :selected_client_ids, updated_client_ids)
    save_filter_state(socket)
    # Push filter update to JavaScript
    push_filter_update(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_ignore_ping", _params, socket) do
    ignore_ping_packets = !socket.assigns.ignore_ping_packets
    socket = assign(socket, :ignore_ping_packets, ignore_ping_packets)
    save_filter_state(socket)
    # Push filter update to JavaScript
    push_filter_update(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_topic_grouping", _params, socket) do
    topic_grouping_enabled = !socket.assigns.topic_grouping_enabled
    socket = assign(socket, :topic_grouping_enabled, topic_grouping_enabled)
    save_filter_state(socket)
    # Push filter update to JavaScript
    push_filter_update(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("export_csv", _params, socket) do
    # Generate server-side CSV export with full payload data
    export_data = generate_export_data(socket, "csv")
    send(self(), {:push_export_event, export_data})
    {:noreply, socket}
  end

  @impl true
  def handle_event("export_excel", _params, socket) do
    # Generate server-side Excel export with full payload data
    export_data = generate_export_data(socket, "excel")
    send(self(), {:push_export_event, export_data})
    {:noreply, socket}
  end

  @impl true
  def handle_event("export_json", _params, socket) do
    # Generate server-side JSON export with full payload data
    export_data = generate_export_data(socket, "json")
    send(self(), {:push_export_event, export_data})
    {:noreply, socket}
  end

  # Helper functions

  defp generate_export_data(socket, format) do
    broker_name = socket.assigns[:active_broker_name] || "unknown"
    timestamp = DateTime.utc_now() |> DateTime.to_iso8601(:basic)

    # Get messages with full payload data from TraceManager for export
    # Limit to last 1000 messages to prevent timeout issues
    messages =
      if socket.assigns[:active_broker_name] do
        case Mqttable.TraceManager.get_messages_for_export(
               socket.assigns[:active_broker_name],
               1000
             ) do
          messages when is_list(messages) ->
            messages

          _ ->
            []
        end
      else
        []
      end

    Logger.debug("Exporting #{length(messages)} messages in #{format} format")

    try do
      case format do
        "csv" ->
          csv_data = generate_csv_data(messages)

          %{
            export_data: csv_data,
            content_type: "text/csv",
            filename: "#{broker_name}_trace_#{timestamp}.csv"
          }

        "excel" ->
          # For Excel, we'll generate CSV format but with .xlsx extension
          # A proper Excel library could be added later if needed
          csv_data = generate_csv_data(messages)

          %{
            export_data: csv_data,
            content_type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            filename: "#{broker_name}_trace_#{timestamp}.xlsx"
          }

        "json" ->
          json_data = generate_json_data(messages)

          %{
            export_data: json_data,
            content_type: "application/json",
            filename: "#{broker_name}_trace_#{timestamp}.json"
          }

        _ ->
          %{
            export_data: "",
            content_type: "text/plain",
            filename: "export.txt"
          }
      end
    rescue
      error ->
        Logger.error("Export failed: #{inspect(error)}")

        %{
          export_data: "Export failed: #{inspect(error)}",
          content_type: "text/plain",
          filename: "export_error.txt"
        }
    end
  end

  defp generate_csv_data(messages) do
    headers = [
      "Packet ID",
      "Timestamp",
      "Client ID",
      "Direction",
      "Type",
      "Topic",
      "Payload",
      "Payload Size",
      "Data Size",
      "QoS",
      "Retain"

    csv_rows =
      messages
      |> Enum.map(&format_message_for_csv/1)
      |> Enum.map(&Enum.join(&1, ","))

    ([Enum.join(headers, ",")] ++ csv_rows)
    |> Enum.join("\n")
  end

  defp generate_json_data(messages) do
    messages
    |> Enum.map(&format_message_for_json/1)
    |> Jason.encode!(pretty: true)
  end

  defp format_message_for_csv(message) do
    [
      escape_csv_field(message[:packet_id] || "N/A"),
      escape_csv_field(format_timestamp_for_export(message[:timestamp])),
      escape_csv_field(message[:client_id] || ""),
      escape_csv_field(format_direction(message[:direction])),
      escape_csv_field(message[:type] || ""),
      escape_csv_field(message[:topic] || "N/A"),
      escape_csv_field(format_payload_for_export(message[:payload])),
      escape_csv_field(message[:payload_size] || 0),
      escape_csv_field(message[:data_size] || 0),
      escape_csv_field(message[:qos] || "-"),
      escape_csv_field(if message[:retain], do: "Yes", else: "No")
  end

  defp format_message_for_json(message) do
    %{
      packet_id: message[:packet_id],
      timestamp: format_timestamp_for_export(message[:timestamp]),
      client_id: message[:client_id],
      direction: format_direction(message[:direction]),
      type: message[:type],
      topic: message[:topic],
      payload: format_payload_for_export(message[:payload]),
      payload_size: message[:payload_size],
      data_size: message[:data_size],
      qos: message[:qos],
      retain: message[:retain],
      properties: message[:properties] || %{}
    }
  end

  defp escape_csv_field(value) when is_binary(value) do
    # Escape quotes and wrap in quotes if contains comma, quote, or newline
    escaped = String.replace(value, "\"", "\"\"")

    if String.contains?(escaped, [",", "\"", "\n", "\r"]) do
      "\"#{escaped}\""
    else
      escaped
    end
  end

  defp escape_csv_field(value), do: to_string(value)

  defp format_timestamp_for_export(timestamp) when is_integer(timestamp) do
    # Convert microsecond timestamp to ISO8601 format
    timestamp
    |> DateTime.from_unix!(:microsecond)
    |> DateTime.to_iso8601()
  end

  defp format_timestamp_for_export(_), do: ""

  defp format_direction(:in), do: "« IN"
  defp format_direction(:out), do: "» OUT"
  defp format_direction(_), do: ""

  defp format_payload_for_export(payload) when is_binary(payload) do
    # For export, include full payload without truncation
    if String.printable?(payload) do
      payload
    else
      # For binary data, show hex representation
      Base.encode16(payload, case: :lower)
    end
  end

  defp format_payload_for_export(_), do: ""

  defp push_grid_data_update(socket) do
    # Push grid data update event to JavaScript Hook
    serialized_data = serialize_grid_data(socket.assigns.grid_data)

    # Send message to parent LiveView to push the event
    send(
      {:push_grid_data_update,
       %{
         grid_data: serialized_data,
         update_type: "replace"
       }}
    )
  end

  defp push_filter_update(_socket) do
    # For LiveComponents, we don't need to send events to JavaScript hooks
    # The data attributes will be updated automatically when the component re-renders
    # and the JavaScript hook's updated() callback will be triggered
    Logger.debug("Filter state updated, component will re-render with new data attributes")
  end

  defp serialize_grid_data(grid_data) when is_list(grid_data) do
    Enum.map(grid_data, &serialize_message/1)
  end

  defp serialize_grid_data(grid_data), do: grid_data

  defp serialize_message(message) when is_map(message) do
    message
    |> Map.update(:topics, [], &serialize_topics/1)
    |> Map.update(:properties, %{}, &serialize_properties/1)
    |> ensure_json_serializable()
  end

  defp serialize_message(message), do: message

  # Serializes a message for detail modal display without payload truncation.
  # This ensures the detail modal shows the complete payload.
  defp serialize_message_for_detail(message) when is_map(message) do
    message
    |> Map.update(:topics, [], &serialize_topics/1)
    |> Map.update(:properties, %{}, &serialize_properties/1)
    |> ensure_json_serializable()
  end

  defp serialize_message_for_detail(message), do: message

  defp serialize_topics(topics) when is_list(topics) do
    Enum.map(topics, fn
      {topic, opts} when is_binary(topic) and is_map(opts) ->
        %{topic: topic, options: opts}

      topic when is_binary(topic) ->
        %{topic: topic, options: %{}}

      other ->
        %{topic: to_string(other), options: %{}}
    end)
  end

  defp serialize_topics(topics), do: topics

  defp serialize_properties(properties) when is_map(properties) do
    # Ensure all property values are JSON-serializable
    Enum.into(properties, %{}, fn {key, value} ->
      {key, ensure_json_value(value)}
    end)
  end

  defp serialize_properties(properties), do: properties

  defp ensure_json_serializable(data) when is_map(data) do
    Enum.into(data, %{}, fn {key, value} ->
      {key, ensure_json_value(value)}
    end)
  end

  defp ensure_json_serializable(data) when is_list(data) do
    Enum.map(data, &ensure_json_value/1)
  end

  defp ensure_json_serializable(data), do: ensure_json_value(data)

  defp ensure_json_value(value) when is_binary(value) do
    # Check if the binary is valid UTF-8
    if String.valid?(value) do
      value
    else
      # Convert invalid UTF-8 binary to a safe representation
      safe_binary_to_string(value)
    end
  end

  defp ensure_json_value(value)
       when is_number(value) or is_boolean(value) or is_nil(value),
       do: value

  defp ensure_json_value(value) when is_atom(value), do: to_string(value)
  defp ensure_json_value(value) when is_list(value), do: Enum.map(value, &ensure_json_value/1)
  defp ensure_json_value(value) when is_map(value), do: ensure_json_serializable(value)

  defp ensure_json_value(value) when is_tuple(value),
    do: Tuple.to_list(value) |> ensure_json_value()

  defp ensure_json_value(value), do: to_string(value)

  # Helper function to safely convert binary data to a string representation
  # Helper function to count unique topics in grid data
  defp count_unique_topics(grid_data) when is_list(grid_data) do
    grid_data
    |> Enum.map(& &1.topic)
    |> Enum.filter(&(&1 != nil and &1 != ""))
    |> Enum.uniq()
    |> length()
  end

  defp count_unique_topics(_), do: 0

  defp safe_binary_to_string(binary) when is_binary(binary) do
    # Try to detect common binary formats
    case binary do
      # Gzip magic number (1f 8b)
      <<0x1F, 0x8B, _rest::binary>> ->
        "<gzipped data (#{byte_size(binary)} bytes)>"

      # PNG magic number
      <<0x89, 0x50, 0x4E, 0x47, _rest::binary>> ->
        "<PNG image (#{byte_size(binary)} bytes)>"

      # JPEG magic number
      <<0xFF, 0xD8, 0xFF, _rest::binary>> ->
        "<JPEG image (#{byte_size(binary)} bytes)>"

      # PDF magic number
      <<"%PDF", _rest::binary>> ->
        "<PDF document (#{byte_size(binary)} bytes)>"

      # Generic binary data
      _ ->
        # For small binaries, show hex representation
        if byte_size(binary) <= 32 do
          hex_string = Base.encode16(binary, case: :lower)
          "<binary: #{hex_string}>"
        else
          # For large binaries, just show size and first few bytes
          <<first_bytes::binary-size(8), _rest::binary>> = binary
          hex_preview = Base.encode16(first_bytes, case: :lower)
          "<binary data: #{hex_preview}... (#{byte_size(binary)} bytes)>"
        end
    end
  end

  defp load_all_data(socket) do
    broker_name = socket.assigns[:active_broker_name]
    Logger.debug("load_all_data called with broker_name: #{inspect(broker_name)}")

    if broker_name do
      case Mqttable.TraceManager.get_messages(broker_name) do
        messages when is_list(messages) ->
          Logger.debug("TraceManager returned #{length(messages)} messages")

          socket
          |> assign(:grid_data, messages)

        error ->
          Logger.error("TraceManager.get_messages failed: #{inspect(error)}")

          socket
          |> assign(:grid_data, [])
      end
    else
      Logger.debug("No broker_name provided, returning empty data")
      socket
    end
  end

  # Copy filter helper functions from the original component
  defp get_broker_client_ids(nil), do: []

  defp get_broker_client_ids(broker_name) do
    # Get all connection sets
    connection_sets = Mqttable.ConnectionSets.get_all()

    # Find the broker by name
    broker =
      Enum.find(connection_sets, fn set ->
        Map.get(set, :name) == broker_name
      end)

    case broker do
      nil ->
        []

      broker ->
        # Extract client IDs from connections in this broker
        broker
        |> Map.get(:connections, [])
        |> Enum.map(fn conn -> Map.get(conn, :client_id) end)
        |> Enum.filter(&(&1 != nil && &1 != ""))
        |> Enum.sort()
    end
  end

  # Generate a unique, sanitized ID for the SlickGrid container
  # This ensures each broker gets a unique grid ID to prevent collisions
  defp generate_grid_id(broker_name) do
    sanitized =
      case broker_name do
        nil ->
          "default"

        "" ->
          "default"

        name ->
          name
          |> String.downcase()
          |> String.replace(~r/[^a-z0-9\-_]/, "-")
          |> String.replace(~r/-+/, "-")
          |> String.trim("-")
          # Limit length to prevent overly long IDs
          |> String.slice(0, 50)
      end

    "trace-slick-grid-#{sanitized}"
  end

  defp load_filter_state(nil) do
  end

  defp load_filter_state(broker_name) do
    # Get UI state from ConnectionSets
    ui_state = Mqttable.ConnectionSets.get_ui_state()
    trace_filters = Map.get(ui_state, :trace_filters, %{})
    broker_filters = Map.get(trace_filters, broker_name, %{})

    # Merge with defaults
    |> Map.merge(broker_filters)
  end

  defp save_filter_state(socket) do
    broker_name = socket.assigns[:active_broker_name]

    if broker_name do
      # Get current UI state
      ui_state = Mqttable.ConnectionSets.get_ui_state()
      trace_filters = Map.get(ui_state, :trace_filters, %{})

      # Update filter state for this broker
      broker_filter_state = %{
        topic_filter: socket.assigns.topic_filter,
        payload_filter: socket.assigns.payload_filter,
        selected_client_ids: socket.assigns.selected_client_ids,
        ignore_ping_packets: socket.assigns.ignore_ping_packets,
        topic_grouping_enabled: socket.assigns.topic_grouping_enabled
      }

      updated_trace_filters = Map.put(trace_filters, broker_name, broker_filter_state)
      updated_ui_state = Map.put(ui_state, :trace_filters, updated_trace_filters)

      # Save to ConnectionSets
      Mqttable.ConnectionSets.update_ui_state(updated_ui_state)
    end
  end

  defp default_filter_state do
    %{
      topic_filter: "",
      payload_filter: "",
      selected_client_ids: [],
      ignore_ping_packets: true,
      topic_grouping_enabled: false
    }
  end
end
