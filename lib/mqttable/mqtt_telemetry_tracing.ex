defmodule Mqttable.MqttTelemetryTracing do
  @moduledoc """
  Module for managing MQTT telemetry tracing through telemetry events.
  This module provides an interface to start, stop, and check the status of
  the telemetry handler for MQTT events.
  """

  require Logger
  alias <PERSON>.PubSub

  alias Mqttable.MqttTelemetryHandler

  @pubsub_topic "mqtt_trace"

  @doc """
  Start the EMQTT telemetry handler.
  Sets the emqtt module log level to debug and starts the telemetry handler.
  """
  def setup do
    # Start the Telemetry Handler
    case MqttTelemetryHandler.start_handler() do
      :ok ->
        Logger.info("EMQTT Telemetry Handler started successfully")
        :ok

      {:error, reason} ->
        Logger.error("Failed to start EMQTT Telemetry Handler: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  Stop the EMQTT telemetry handler.
  Stops the telemetry handler and cleans up the emqtt module log level.
  """
  def cleanup do
    # Stop the Telemetry Handler
    result = MqttTelemetryHandler.stop_handler()

    case result do
      :ok ->
        Logger.info("EMQTT Telemetry Handler stopped successfully")
        :ok

      {:error, reason} ->
        Logger.error("Failed to stop EMQTT Telemetry Handler: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  Check the status of the EMQTT telemetry handler.
  Returns true if the handler is running, false otherwise.
  """
  def status do
    MqttTelemetryHandler.handler_running?()
  end

  @doc """
  Get detailed status information about the telemetry handler.
  Returns a map with handler status information.
  """
  def detailed_status do
    running = MqttTelemetryHandler.handler_running?()

    %{
      telemetry_handler_running: running,
      handler_id: if(running, do: "emqtt-telemetry-handler", else: nil),
      events_handled: [
        "emqtt.websocket.recv_data",
        "emqtt.socket.recv_data",
        "emqtt.socket.send_data",
        "emqtt.socket.send_data_failed"
    }
  end

  @doc """
  Subscribe to MQTT trace messages.
  """
  def subscribe do
    PubSub.subscribe(Mqttable.PubSub, @pubsub_topic)
  end

  @doc """
  Start the telemetry handler (alias for setup/0).
  """
  def start_handler do
  end

  @doc """
  Stop the telemetry handler (alias for cleanup/0).
  """
  def stop_handler do
  end

  @doc """
  Restart the telemetry handler.
  """
  def restart do
    case cleanup() do
      :ok -> setup()
      {:error, _reason} = error -> error
    end
  end
end
