defmodule Mqttable.MqttClient.EtsOperations do
  @moduledoc """
  Centralized ETS operations for MQTT client management.

  This module provides a clean interface for all ETS table operations
  related to MQTT client records. It encapsulates the ETS table structure
  and provides type-safe operations for client record management.
  """

  # Type definitions
  @type broker_name :: String.t()
  @type client_id :: String.t()
  @type worker_pid :: pid()
  @type client_pid :: pid() | nil
  @type mqtt_opts :: keyword() | nil
  @type parse_state :: term()
  @type client_status :: :connected | :disconnected | :reconnecting | :connecting
  @type client_record ::
          {{broker_name(), client_id()}, worker_pid(), client_pid(), mqtt_opts(), parse_state(),
           client_status()}

  # ETS table name
  @table_name Mqttable.MqttClient.Manager

  def store_client_record(broker_name, client_id, worker_pid, client_pid, mqtt_opts, status) do
    opts = mqtt_opts || []
    parse_state = build_parse_state(opts)

    record = {{broker_name, client_id}, worker_pid, client_pid, opts, parse_state, status}
    :ets.insert(@table_name, record)
  end

  @doc """
  Removes a client record from the ETS table.
  """
    def remove_client_record(broker_name, client_id) do
    :ets.delete(@table_name, {broker_name, client_id})
  end

  @doc """
  Looks up the status of a client.
  Returns :disconnected if the client is not found.
  """
    def lookup_client_status(broker_name, client_id) do
    case :ets.lookup(@table_name, {broker_name, client_id}) do
      [{{_, _}, _worker_pid, _client_pid, _mqtt_opts, _parse_state, status}] ->
        status

      [] ->
        :disconnected
    end
  end

  @doc """
  Looks up the parse state for a client.
  Returns nil if the client is not found.
  """
    def lookup_client_parse_state(broker_name, client_id) do
    case :ets.lookup(@table_name, {broker_name, client_id}) do
      [{{_, _}, _worker_pid, _client_pid, _mqtt_opts, parse_state, _status}] ->
        parse_state

      [] ->
        nil
    end
  end

  @doc """
  Updates the parse state for a client.
  """
    def store_client_parse_state(broker_name, client_id, parse_state) do
    :ets.update_element(@table_name, {broker_name, client_id}, {5, parse_state})
  end

  @doc """
  Looks up a complete client record.
  Returns a tuple with status, worker_pid, client_pid, and mqtt_opts.
  """
            {client_status(), worker_pid() | nil, client_pid(), mqtt_opts()}
  def lookup_client_record(broker_name, client_id) do
    case :ets.lookup(@table_name, {broker_name, client_id}) do
      [{{_, _}, worker_pid, client_pid, mqtt_opts, _parse_state, status}] ->
        {status, worker_pid, client_pid, mqtt_opts}

      [] ->
        {:disconnected, nil, nil, nil}
    end
  end

  @doc """
  Gets all client records from the ETS table.
  """
    def get_all_client_records do
    :ets.tab2list(@table_name)
  end

  @doc """
  Gets all active clients as a map of client_id to worker_pid.
  """
    def get_all_clients do
    |> Enum.map(fn {client, worker_pid, _client_pid, _mqtt_opts, _parse_state, _status} ->
      {client, worker_pid}
    end)
    |> Enum.into(%{})
  end

  def get_connected_clients do
    |> Enum.filter(fn {_client_id, _worker_pid, _client_pid, _mqtt_opts, _parse_state, status} ->
      status == :connected
    end)
    |> Enum.map(fn {{broker_name, client_id}, _worker_pid, _client_pid, mqtt_opts, _parse_state,
                    status} ->
      mqtt_version = extract_mqtt_version_from_opts(mqtt_opts)

      %{
        broker_name: broker_name,
        client_id: client_id,
        status: status,
        mqtt_version: mqtt_version
      }
    end)
  end

  @doc """
  Updates the client PID and status for an existing record.
  """
  def update_client_connection(broker_name, client_id, client_pid, mqtt_opts, status) do
    opts = mqtt_opts || []
    parse_state = build_parse_state(opts)

    updates = [
      # client_pid position
      {3, client_pid},
      # mqtt_opts position
      {4, opts},
      # parse_state position
      {5, parse_state},
      # status position
      {6, status}

    :ets.update_element(@table_name, {broker_name, client_id}, updates)
  end

  @doc """
  Updates only the status of a client record.
  """
    def update_client_status(broker_name, client_id, status) do
    :ets.update_element(@table_name, {broker_name, client_id}, {6, status})
  end

  @doc """
  Checks if a client record exists in the ETS table.
  """
    def client_exists?(broker_name, client_id) do
    case :ets.lookup(@table_name, {broker_name, client_id}) do
      [] -> false
      _ -> true
    end
  end

  # Private helper functions

    defp build_parse_state(opts) do
    max_size = Map.get(Keyword.get(opts, :properties, %{}), :"Receive-Maximum", 0xFFFFFFF)

    version =
      case Keyword.get(opts, :proto_ver, :v5) do
        :v5 -> 5
        _ -> 4
      end

    :emqtt_frame.initial_parse_state(%{max_size: max_size, version: version})
  end

    defp extract_mqtt_version_from_opts(nil), do: "5.0"

  defp extract_mqtt_version_from_opts(mqtt_opts) when is_list(mqtt_opts) do
    case Keyword.get(mqtt_opts, :proto_ver, :v5) do
      :v5 -> "5.0"
      :v4 -> "3.1.1"
      :v3 -> "3.1"
      _ -> "5.0"
    end
  end

  defp extract_mqtt_version_from_opts(_), do: "5.0"
end
