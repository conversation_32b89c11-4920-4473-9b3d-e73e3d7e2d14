defmodule Mqttable.MqttClient.ErrorHandler do
  @moduledoc """
  Centralized error handling and message formatting for MQTT client operations.

  This module provides pure functions for error classification, message formatting,
  and error recovery strategies. All functions are side-effect free and focus on
  transforming error data into user-friendly messages.
  """

  # Type definitions
  @type error_reason :: atom() | {atom(), term()}
  @type error_message :: String.t()
  @type error_classification :: :connection | :authentication | :protocol | :network | :unknown

  @doc """
  Formats MQTT error reasons into user-friendly error messages.

  ## Examples

      iex> ErrorHandler.format_mqtt_error(:econnrefused)
      "Connection refused: The broker is not reachable or not running"
      
      iex> ErrorHandler.format_mqtt_error(:timeout)
      "Connection timeout: The broker did not respond in time"
  """
    def format_mqtt_error(reason) do
    reason
    |> classify_error()
    |> format_by_classification(reason)
  end

  @doc """
  Classifies error reasons into categories for better handling.
  """
    def classify_error(reason) do
    case reason do
      reason
      when reason in [
             :econnrefused,
             :timeout,
             :nxdomain,
             :econnreset,
             :closed,
             :etimedout,
             :ehostunreach,
             :enetunreach,
             :tcp_closed
           ] ->
        :connection

      reason when reason in [:not_authorized, :bad_username_or_password, :eacces] ->
        :authentication

      reason when reason in [:protocol_error, :einval, :badarg, :already_present] ->
        :protocol

      reason when reason in [:unknown_ca, :cert_verify_failed] ->
        :network

      _ ->
        :unknown
    end
  end

  @doc """
  Extracts concise error messages from complex error tuples.
  """
    def extract_concise_error_message(reason) do
    case reason do
      {:shutdown, :tcp_closed} ->
        "Connection closed by broker before completing handshake"

      {:socket_closed_before_connack, _} ->
        "Connection closed by broker before completing handshake"

      {:badmatch, {:error, error_reason}} when is_atom(error_reason) ->
        format_mqtt_error(error_reason)

      reason when is_atom(reason) ->
        format_mqtt_error(reason)

      _ ->
        "Unexpected error: #{inspect(reason)}"
    end
  end

  @doc """
  Checks if an error is recoverable through reconnection.
  """
    def recoverable_error?(reason) do
    case classify_error(reason) do
      :connection -> true
      :network -> true
      :authentication -> false
      :protocol -> false
      :unknown -> true
    end
  end

  @doc """
  Suggests retry delay based on error type.
  Returns delay in milliseconds.
  """
    def suggest_retry_delay(reason) do
    case classify_error(reason) do
      # 10 seconds for connection errors
      :connection -> 10_000
      # 30 seconds for network errors
      :network -> 30_000
      # Don't retry auth errors
      :authentication -> 0
      # Don't retry protocol errors
      :protocol -> 0
      # 15 seconds for unknown errors
      :unknown -> 15_000
    end
  end

  # Private helper functions

    defp format_by_classification(:connection, reason) do
    format_connection_error(reason)
  end

  defp format_by_classification(:authentication, reason) do
    format_authentication_error(reason)
  end

  defp format_by_classification(:protocol, reason) do
    format_protocol_error(reason)
  end

  defp format_by_classification(:network, reason) do
    format_network_error(reason)
  end

  defp format_by_classification(:unknown, reason) do
    "Connection error: #{inspect(reason)}"
  end

    defp format_connection_error(reason) do
    case reason do
      :econnrefused ->
        "Connection refused: The broker is not reachable or not running"

      :timeout ->
        "Connection timeout: The broker did not respond in time"

      :nxdomain ->
        "Host not found: The broker hostname could not be resolved"

      :econnreset ->
        "Connection reset: The connection was forcibly closed by the broker"

      :closed ->
        "Connection closed: The connection was closed unexpectedly"

      :etimedout ->
        "Connection timed out: The operation timed out"

      :ehostunreach ->
        "Host unreachable: The broker host is unreachable"

      :enetunreach ->
        "Network unreachable: The network is unreachable"

      :tcp_closed ->
        "Connection closed: The broker closed the connection before completing the handshake"

      {:shutdown, :tcp_closed} ->
        "Connection closed: The broker closed the connection before completing the handshake"

      {:socket_closed_before_connack, _} ->
        "Connection closed: The broker closed the connection before completing the handshake"

      _ ->
        "Connection error: #{inspect(reason)}"
    end
  end

    defp format_authentication_error(reason) do
    case reason do
      :not_authorized ->
        "Not authorized: Authentication failed, check your username and password"

      :bad_username_or_password ->
        "Bad username or password: Authentication failed, check your credentials"

      :eacces ->
        "Permission denied: Access to the broker was denied"

      _ ->
        "Authentication error: #{inspect(reason)}"
    end
  end

    defp format_protocol_error(reason) do
    case reason do
      :protocol_error ->
        "Protocol error: There was an error in the MQTT protocol"

      :einval ->
        "Invalid argument: One of the connection parameters is invalid"

      :badarg ->
        "Bad argument: One of the connection parameters is invalid"

      :already_present ->
        "Client ID already in use: Another client with the same ID is already connected"

      _ ->
        "Protocol error: #{inspect(reason)}"
    end
  end

    defp format_network_error(reason) do
    case reason do
      :unknown_ca ->
        "Unknown CA: The server's certificate authority is unknown"

      :cert_verify_failed ->
        "Certificate verification failed: The server's certificate could not be verified"

      _ ->
        "Network error: #{inspect(reason)}"
    end
  end
end
