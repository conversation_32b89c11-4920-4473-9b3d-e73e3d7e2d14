defmodule Mqttable.MqttClient.ConnectionBuilder do
  @moduledoc """
  Pure functions for building MQTT connection options.

  This module provides composable functions for constructing MQTT client
  configuration options from connection and broker parameters. All functions
  are pure and side-effect free, making them easily testable and composable.
  """

  alias MqttableWeb.Utils.ConnectionHelpers

  # Type definitions
  @type connection :: map()
  @type broker :: map()
  @type mqtt_opts :: keyword()
  @type mqtt_properties :: map()

  @doc """
  Builds complete MQTT connection options from connection and broker parameters.

  ## Examples

      iex> connection = %{client_id: "test", mqtt_version: "5.0"}
      iex> broker = %{host: "localhost", port: "1883", protocol: "mqtt"}
      iex> opts = ConnectionBuilder.build_mqtt_options(connection, broker)
      iex> Keyword.get(opts, :clientid)
      "test"
  """
    def build_mqtt_options(connection, broker) do
    connection
    |> ConnectionHelpers.prepare_connection_for_mqtt()
    |> build_base_options(broker)
    |> add_authentication_options(connection)
    |> add_protocol_options(broker)
    |> add_mqtt_version_options(connection)
  end

  def build_base_options(connection, broker) do
    [
      clientid: connection.client_id,
      clean_start: connection.clean_start,
      keepalive: connection.keep_alive,
      name: String.to_atom(connection.name),
      broker_name: broker.name,
      proto_ver: mqtt_version_to_proto_ver(connection.mqtt_version)
  end

  @doc """
  Adds authentication options to the base options.
  """
    def add_authentication_options(base_opts, connection) do
    base_opts
    |> add_username(connection)
    |> add_password(connection)
    |> add_will_message(connection)
  end

  @doc """
  Adds protocol-specific options based on broker configuration.
  """
    def add_protocol_options(opts, broker) do
    opts
    |> add_host_and_port(broker)
    |> add_transport_options(broker)
  end

  @doc """
  Adds MQTT version-specific options and properties.
  """
    def add_mqtt_version_options(opts, connection) do
    case connection.mqtt_version do
      "5.0" ->
        properties = build_mqtt5_properties(connection)

        if map_size(properties) > 0 do
          [{:properties, properties} | opts]
        else
          opts
        end

      _ ->
        opts
    end
  end

  # Private helper functions

    defp mqtt_version_to_proto_ver(mqtt_version) do
    case mqtt_version do
      "5.0" -> :v5
      "3.1.1" -> :v4
      "3.1" -> :v3
      _ -> :v4
    end
  end

    defp add_host_and_port(opts, broker) do
    [
      {:host, String.to_charlist(broker.host)},
      {:port, String.to_integer(broker.port)}
      | opts
  end

    defp add_transport_options(opts, broker) do
    case broker.protocol do
      "mqtts" ->
        ssl_opts = build_ssl_options(broker)
        [{:ssl, true}, {:ssl_opts, ssl_opts} | opts]

      "wss" ->
        ws_path = Map.get(broker, :ws_path, "/mqtt") |> String.to_charlist()
        ssl_opts = build_ssl_options(broker)
        # For WSS, SSL options need to be passed via ws_transport_options to Gun library
        # Force HTTP/1.1 protocol as some servers don't support WebSocket over HTTP/2
        ws_transport_opts = [
          {:transport, :tls},
          {:tls_opts, ssl_opts},
          # Force HTTP/1.1
          {:protocols, [:http]}

        # Try without specifying subprotocol - let server decide
        # Some servers are picky about WebSocket subprotocol negotiation
        ws_headers = []

        [
          {:ws_path, ws_path},
          {:ws_transport_options, ws_transport_opts},
          {:ws_headers, ws_headers}
          | opts

      "ws" ->
        ws_path = Map.get(broker, :ws_path, "/mqtt") |> String.to_charlist()
        # Try without specifying subprotocol for ws connections too
        ws_headers = []

        [{:ws_path, ws_path}, {:ws_headers, ws_headers} | opts]

      _ ->
        opts
    end
  end

    defp build_ssl_options(broker) do
    ssl_opts = []

    # Add verification settings based on certificate configuration
    ssl_opts = add_ssl_verification(ssl_opts, broker)

    # Add CA certificate file if provided
    ssl_opts = add_ca_certificate(ssl_opts, broker)

    # Add client certificate and key if provided
    ssl_opts = add_client_certificates(ssl_opts, broker)

    # Add ALPN protocol if specified
    ssl_opts = add_alpn_protocol(ssl_opts, broker)

    ssl_opts
  end

    defp add_ssl_verification(ssl_opts, broker) do
    # Check if SSL verification is enabled
    if Map.get(broker, :ssl_enabled, false) do
      # For CA-signed certificates, enable peer verification
      ca_file = Map.get(broker, :ca_file, "")

      if ca_file != "" do
        [{:verify, :verify_peer} | ssl_opts]
      else
        # No CA file provided, disable verification
        [{:verify, :verify_none} | ssl_opts]
      end
    else
      # SSL verification disabled, skip all certificate validation
      [{:verify, :verify_none} | ssl_opts]
    end
  end

    defp add_ca_certificate(ssl_opts, broker) do
    ca_file = Map.get(broker, :ca_file, "")

    if ca_file != "" do
      # Convert relative path to absolute path
      ca_file_path = resolve_certificate_path(ca_file)
      [{:cacertfile, String.to_charlist(ca_file_path)} | ssl_opts]
    else
      ssl_opts
    end
  end

    defp add_client_certificates(ssl_opts, broker) do
    client_cert_file = Map.get(broker, :client_cert_file, "")
    client_key_file = Map.get(broker, :client_key_file, "")

    ssl_opts =
      if client_cert_file != "" do
        cert_file_path = resolve_certificate_path(client_cert_file)
        [{:certfile, String.to_charlist(cert_file_path)} | ssl_opts]
      else
        ssl_opts
      end

    ssl_opts =
      if client_key_file != "" do
        key_file_path = resolve_certificate_path(client_key_file)
        [{:keyfile, String.to_charlist(key_file_path)} | ssl_opts]
      else
        ssl_opts
      end

    ssl_opts
  end

    defp add_alpn_protocol(ssl_opts, broker) do
    alpn = Map.get(broker, :alpn, "")

    if alpn != "" do
      [{:alpn_advertised_protocols, [String.to_charlist(alpn)]} | ssl_opts]
    else
      ssl_opts
    end
  end

    defp resolve_certificate_path(path) when is_binary(path) do
    if String.starts_with?(path, "/certificates/") do
      # Remove the leading "/certificates/" and build absolute path
      filename = String.replace_prefix(path, "/certificates/", "")
      Path.join([:code.priv_dir(:mqttable), "data", "certificates", filename])
    else
      # Assume it's already an absolute path or relative to current directory
      path
    end
  end

    defp add_username(opts, connection) do
    if connection.username && connection.username != "" do
      [{:username, connection.username} | opts]
    else
      opts
    end
  end

    defp add_password(opts, connection) do
    if connection.password && connection.password != "" do
      [{:password, connection.password} | opts]
    else
      opts
    end
  end

    defp add_will_message(opts, connection) do
    if connection.will_topic && connection.will_topic != "" do
      build_will_options(opts, connection)
    else
      opts
    end
  end

    defp build_will_options(opts, connection) do
    will_opts = [
      will_topic: connection.will_topic,
      will_payload: connection.will_payload || "",
      will_qos: String.to_integer(connection.will_qos || "0"),
      will_retain: connection.will_retain

    opts = Keyword.merge(opts, will_opts)

    if connection.mqtt_version == "5.0" do
      add_mqtt5_will_properties(opts, connection)
    else
      opts
    end
  end

    defp add_mqtt5_will_properties(opts, connection) do
    will_props = build_mqtt5_will_properties(connection)

    if map_size(will_props) > 0 do
      [{:will_props, will_props} | opts]
    else
      opts
    end
  end

  @doc """
  Builds MQTT 5.0 connection properties from connection parameters.
  """
    def build_mqtt5_properties(connection) do
    %{}
    |> add_session_expiry_interval(connection)
    |> add_receive_maximum(connection)
    |> add_maximum_packet_size(connection)
    |> add_topic_alias_maximum(connection)
    |> add_request_response_info(connection)
    |> add_request_problem_info(connection)
    |> add_user_properties(connection)
  end

  @doc """
  Builds MQTT 5.0 will message properties from connection parameters.
  """
    def build_mqtt5_will_properties(connection) do
    %{}
    |> add_will_delay_interval(connection)
    |> add_payload_format_indicator(connection)
    |> add_message_expiry_interval(connection)
    |> add_content_type(connection)
    |> add_response_topic(connection)
    |> add_correlation_data(connection)
  end

  # MQTT 5.0 connection properties helpers

    defp add_session_expiry_interval(props, connection) do
    if is_integer(connection.session_expiry_interval) && connection.session_expiry_interval > 0 do
      Map.put(props, :"Session-Expiry-Interval", connection.session_expiry_interval)
    else
      props
    end
  end

    defp add_receive_maximum(props, connection) do
    if is_integer(connection.receive_maximum) && connection.receive_maximum > 0 do
      Map.put(props, :"Receive-Maximum", connection.receive_maximum)
    else
      props
    end
  end

    defp add_maximum_packet_size(props, connection) do
    if is_integer(connection.maximum_packet_size) && connection.maximum_packet_size > 0 do
      Map.put(props, :"Maximum-Packet-Size", connection.maximum_packet_size)
    else
      props
    end
  end

    defp add_topic_alias_maximum(props, connection) do
    if is_integer(connection.topic_alias_maximum) && connection.topic_alias_maximum > 0 do
      Map.put(props, :"Topic-Alias-Maximum", connection.topic_alias_maximum)
    else
      props
    end
  end

    defp add_request_response_info(props, connection) do
    if connection.request_response_info do
      Map.put(props, :"Request-Response-Information", 1)
    else
      props
    end
  end

    defp add_request_problem_info(props, connection) do
    if connection.request_problem_info do
      Map.put(props, :"Request-Problem-Information", 1)
    else
      props
    end
  end

    defp add_user_properties(props, connection) do
    if connection.user_properties && is_list(connection.user_properties) &&
         length(connection.user_properties) > 0 do
      valid_props =
        connection.user_properties
        |> Enum.filter(&valid_user_property?/1)
        |> Enum.map(fn prop -> {prop.key, prop.value} end)

      if length(valid_props) > 0 do
        Map.put(props, :"User-Property", valid_props)
      else
        props
      end
    else
      props
    end
  end

    defp valid_user_property?(prop) do
    is_map(prop) && Map.has_key?(prop, :key) && Map.has_key?(prop, :value) &&
      prop.key != "" && prop.value != ""
  end

  # MQTT 5.0 will properties helpers

    defp add_will_delay_interval(props, connection) do
    if is_integer(connection.will_delay_interval) && connection.will_delay_interval > 0 do
      Map.put(props, :"Will-Delay-Interval", connection.will_delay_interval)
    else
      props
    end
  end

    defp add_payload_format_indicator(props, connection) do
    if connection.will_payload_format do
      Map.put(props, :"Payload-Format-Indicator", 1)
    else
      props
    end
  end

    defp add_message_expiry_interval(props, connection) do
    if is_integer(connection.will_message_expiry) && connection.will_message_expiry > 0 do
      Map.put(props, :"Message-Expiry-Interval", connection.will_message_expiry)
    else
      props
    end
  end

    defp add_content_type(props, connection) do
    if connection.will_content_type && connection.will_content_type != "" do
      Map.put(props, :"Content-Type", connection.will_content_type)
    else
      props
    end
  end

    defp add_response_topic(props, connection) do
    if connection.will_response_topic && connection.will_response_topic != "" do
      Map.put(props, :"Response-Topic", connection.will_response_topic)
    else
      props
    end
  end

    defp add_correlation_data(props, connection) do
    if connection.will_correlation_data && connection.will_correlation_data != "" do
      Map.put(props, :"Correlation-Data", connection.will_correlation_data)
    else
      props
    end
  end
end
