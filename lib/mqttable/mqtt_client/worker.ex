defmodule Mqttable.MqttClient.Worker do
  @moduledoc """
  Worker process for managing individual MQTT client connections.
  Each worker handles one MQTT client connection and manages its own reconnection logic.
  """
  use GenServer
  require Logger

  alias Phoenix.PubSub

  alias Mqttable.MqttClient.{
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ts<PERSON><PERSON><PERSON>,
    SubscriptionManager
  }

  alias Mqttable.Uploads.FileStorage

  # Constants
  @pubsub_topic "mqtt_clients"

  # Client status values
  @status_connecting :connecting
  @status_connected :connected
  @status_disconnected :disconnected
  @status_reconnecting :reconnecting

  # Type definitions
  @type client_id :: String.t()
  @type client_pid :: pid()
  @type client_status :: :connected | :disconnected | :reconnecting
  @type error_reason :: atom() | {atom(), term()}
  @type error_message :: String.t()

  # Worker state
  @type state :: %{
          client_id: client_id(),
          connection: map(),
          broker: map(),
          mqtt_client_pid: client_pid() | nil,
          mqtt_opts: keyword() | nil,
          status: client_status(),
          reconnect_timer: reference() | nil,
          scheduled_message_timers: %{integer() => reference()}
        }

  # Client API

  @doc """
  Starts a worker for the given client connection.
  """
    def start_link({broker, connection}) do
    client_id = connection.client_id
    broker_name = broker.name

    GenServer.start_link(__MODULE__, {broker, connection},
      name: via_tuple(broker_name, client_id)
    )
  end

  def subscribe(broker_name, client_id, topic, opts \\ []) do
    case get_worker_pid(broker_name, client_id) do
      {:ok, worker_pid} ->
        GenServer.call(worker_pid, {:subscribe, topic, opts})

      {:error, :not_found} ->
        {:error, :not_connected}
    end
  end

  def unsubscribe(broker_name, client_id, topic) do
    case get_worker_pid(broker_name, client_id) do
      {:ok, worker_pid} ->
        GenServer.call(worker_pid, {:unsubscribe, topic})

      {:error, :not_found} ->
        {:error, :not_connected}
    end
  end

  def publish(broker_name, client_id, topic, payload, opts \\ []) do
    case get_worker_pid(broker_name, client_id) do
      {:ok, worker_pid} ->
        GenServer.call(worker_pid, {:publish, topic, payload, opts})

      {:error, :not_found} ->
        {:error, :not_connected}
    end
  end

  def get_startup_args(broker_name, client_id) do
    case get_worker_pid(broker_name, client_id) do
      {:ok, worker_pid} ->
        GenServer.call(worker_pid, {:get_startup_args})

      {:error, :not_found} ->
        {:error, :not_found}
    end
  end

  def disconnect(broker_name, client_id) do
    case get_worker_pid(broker_name, client_id) do
      {:ok, worker_pid} ->
        GenServer.call(worker_pid, :disconnect)

      {:error, :not_found} ->
        {:error, :not_connected}
    end
  end

  def stop_all_scheduled_messages(broker_name, client_id) do
    case get_worker_pid(broker_name, client_id) do
      {:ok, worker_pid} ->
        GenServer.call(worker_pid, {:stop_all_scheduled_messages})

      {:error, :not_found} ->
        {:error, :not_found}
    end
  end

  def sync_scheduled_messages(broker_name, client_id, scheduled_messages) do
    case get_worker_pid(broker_name, client_id) do
      {:ok, worker_pid} ->
        GenServer.call(worker_pid, {:sync_scheduled_messages, scheduled_messages})

      {:error, :not_found} ->
        {:error, :not_found}
    end
  end

  def get_debug_info(broker_name, client_id) do
    case get_worker_pid(broker_name, client_id) do
      {:ok, worker_pid} ->
        GenServer.call(worker_pid, {:get_debug_info})

      {:error, :not_found} ->
        {:error, :not_found}
    end
  end

  # Server callbacks

  @impl true
  def init({broker, connection}) do
    :erlang.process_flag(:trap_exit, true)

    state = %{
      client_id: connection.client_id,
      connection: connection,
      broker: broker,
      mqtt_client_pid: nil,
      mqtt_opts: nil,
      status: @status_disconnected,
      reconnect_timer: nil,
      scheduled_message_timers: %{}
    }

    EtsOperations.store_client_record(
      broker.name,
      state.client_id,
      nil,
      nil,
      @status_connecting
    )

    {:ok, state, {:continue, :connect}}
  end

  @impl true
  def handle_continue(:connect, state) do
    handle_info(:connect, state)
  end

  @impl true
  def handle_call({:subscribe, topic, opts}, _from, state) do
    case state.mqtt_client_pid do
      nil ->
        {:reply, {:error, :not_connected}, state}

      client_pid ->
        # Default subscription options
        sub_opts = Keyword.get(opts, :sub_opts, [{:qos, 0}])

        # Process subscription options to convert nl and rap values from 0/1 to false/true
        sub_opts = SubscriptionManager.process_subscription_options(sub_opts)

        # Extract subscription identifier if provided
        sub_id = Keyword.get(opts, :id)

        # Prepare properties map with subscription identifier if provided
        props = SubscriptionManager.prepare_subscription_properties(sub_id)

        # Validate topic before subscribing
        case SubscriptionManager.validate_topic_filter(topic) do
          :ok ->
            # Subscribe to the topic
            try do
              case :mqtt_client.subscribe(client_pid, props, [{topic, sub_opts}]) do
                {:ok, props, reason_codes} ->
                  # Check if the subscription was successful based on reason codes
                  case SubscriptionManager.check_subscription_success(reason_codes) do
                    :ok ->
                      # Broadcast topic subscription
                      broadcast_topic_subscription(
                        state.broker.name,
                        state.client_id,
                        topic,
                        sub_opts,
                        sub_id
                      )

                      {:reply, {:ok, props, reason_codes}, state}

                    {:error, error_message} ->
                      Logger.error("Failed to subscribe to topic #{topic}: #{error_message}")
                      {:reply, {:error, :subscription_rejected, error_message}, state}
                  end

                {:error, reason} ->
                  error_message = ErrorHandler.format_mqtt_error(reason)

                  Logger.error(
                    "Failed to subscribe to topic #{topic}: #{inspect(reason)} - #{error_message}"
                  )

                  {:reply, {:error, reason, error_message}, state}
              end
            catch
              :exit, reason ->
                error_message = "Subscription failed: #{inspect(reason)}"
                Logger.error(error_message)
                {:reply, {:error, reason, error_message}, state}
            end

          {:error, error_message} ->
            {:reply, {:error, :invalid_topic, error_message}, state}
        end
    end
  end

  @impl true
  def handle_call({:unsubscribe, topic}, _from, state) do
    case state.mqtt_client_pid do
      nil ->
        {:reply, {:error, :not_connected}, state}

      client_pid ->
        try do
          case :mqtt_client.unsubscribe(client_pid, %{}, [topic]) do
            {:ok, props, reason_codes} ->
              # Broadcast topic unsubscription
              broadcast_topic_unsubscription(state.broker.name, state.client_id, topic)
              {:reply, {:ok, props, reason_codes}, state}

            {:error, reason} ->
              error_message = ErrorHandler.format_mqtt_error(reason)

              Logger.error(
                "Failed to unsubscribe from topic #{topic}: #{inspect(reason)} - #{error_message}"
              )

              {:reply, {:error, reason, error_message}, state}
          end
        catch
          :exit, reason ->
            error_message = "Unsubscription failed: #{inspect(reason)}"
            Logger.error(error_message)
            {:reply, {:error, reason, error_message}, state}
        end
    end
  end

  @impl true
  def handle_call({:publish, topic, payload, opts}, _from, state) do
    case state.mqtt_client_pid do
      nil ->
        {:reply, {:error, :not_connected}, state}

      client_pid ->
        # Extract publish options
        qos = Keyword.get(opts, :qos, 0)
        retain = Keyword.get(opts, :retain, false)
        properties = Keyword.get(opts, :properties, %{})
        opts = [{:qos, qos}, {:retain, retain}]

        try do
          case :mqtt_client.publish(client_pid, topic, properties, payload, opts) do
            {:ok, packet_id} when is_map(packet_id) ->
              # Check reason code for QoS 1/2 messages and log appropriately
              case Map.get(packet_id, :reason_code, 0) do
                0 ->
                  # Success
                  Logger.debug(
                    "Published message to topic #{inspect(topic)} with packet_id #{inspect(packet_id)}"
                  )

                reason_code ->
                  # Failure - reason code indicates error, but still return {:ok, packet_id}
                  # so the UI can handle the reason code properly
                  reason_name = Map.get(packet_id, :reason_code_name, "unknown")

                  Logger.error(
                    "Published message failed: #{reason_name} (code: #{reason_code}) for topic #{topic}"
                  )
              end

              # Always return {:ok, packet_id} - let the UI handle reason code checking
              {:reply, {:ok, packet_id}, state}

            :ok ->
              # QoS 0 messages return :ok without packet_id
              Logger.debug("Published QoS 0 message to topic #{topic}")
              {:reply, {:ok, 0}, state}

            {:error, reason} ->
              error_message = ErrorHandler.format_mqtt_error(reason)

              Logger.error(
                "Failed to publish to topic #{topic}: #{inspect(reason)} - #{error_message}"
              )

              {:reply, {:error, reason, error_message}, state}
          end
        catch
          :exit, reason ->
            error_message = "Publish failed: #{inspect(reason)}"
            Logger.error(error_message)
            {:reply, {:error, reason, error_message}, state}
        end
    end
  end

  @impl true
  def handle_call({:get_startup_args}, _from, state) do
    startup_args = {state.broker, state.connection}
    {:reply, {:ok, startup_args}, state}
  end

  @impl true
  def handle_call(:disconnect, _from, state) do
    case state.mqtt_client_pid do
      nil ->
        # Already disconnected
        {:reply, :ok, state}

      client_pid ->
        # Gracefully disconnect the MQTT client
        do_disconnect(client_pid)

        # Cancel all scheduled message timers
        cancel_scheduled_message_timers(state)

        # Update state and ETS record
        new_state = %{
          state
          | mqtt_client_pid: nil,
            status: @status_disconnected,
            scheduled_message_timers: %{}
        }

        EtsOperations.store_client_record(
          state.broker.name,
          state.client_id,
          nil,
          nil,
          @status_disconnected
        )

        # Broadcast status change
        broadcast_status_change(state.broker.name, state.client_id, @status_disconnected)

        {:reply, :ok, new_state}
    end
  end

  @impl true
  def handle_call({:get_debug_info}, _from, state) do
    debug_info = %{
      client_id: state.client_id,
      status: state.status,
      scheduled_messages_count: length(Map.get(state.connection, :scheduled_messages, [])),
      scheduled_messages: Map.get(state.connection, :scheduled_messages, []),
      active_timers_count: map_size(state.scheduled_message_timers),
      active_timers: state.scheduled_message_timers,
      mqtt_client_pid: state.mqtt_client_pid
    }

    {:reply, {:ok, debug_info}, state}
  end

  @impl true
  def handle_call({:stop_all_scheduled_messages}, _from, state) do
    # Cancel all timers
    Enum.each(state.scheduled_message_timers, fn {_index, timer_ref} ->
      Process.cancel_timer(timer_ref)
    end)

    new_state = %{state | scheduled_message_timers: %{}}
    Logger.info("Stopped all scheduled messages for client #{state.client_id}")
    {:reply, :ok, new_state}
  end

  @impl true
  def handle_call({:sync_scheduled_messages, scheduled_messages}, _from, state) do
    current_scheduled_messages = Map.get(state.connection, :scheduled_messages, [])

    # Only reset timers if the scheduled messages have actually changed
    if scheduled_messages != current_scheduled_messages do
      # Stop all existing timers
      Enum.each(state.scheduled_message_timers, fn {_index, timer_ref} ->
        Process.cancel_timer(timer_ref)
      end)

      # Start new timers for the provided scheduled messages
      updated_timers =
        scheduled_messages
        |> Enum.with_index()
        |> Enum.reduce(%{}, fn {scheduled_message, index}, acc ->
          interval_ms = get_scheduled_message_interval(scheduled_message)

          timer_ref =
            Process.send_after(
              {:send_scheduled_message, index, scheduled_message},
              interval_ms
            )

          Map.put(acc, index, timer_ref)
        end)

      # Update both timers and connection scheduled messages
      updated_connection = Map.put(state.connection, :scheduled_messages, scheduled_messages)

      new_state = %{
        state
        | scheduled_message_timers: updated_timers,
          connection: updated_connection
      }

      Logger.info(
        "Synchronized #{length(scheduled_messages)} scheduled messages for client #{state.client_id} (changes detected)"
      )

      {:reply, :ok, new_state}
    else
      # No changes, keep existing timers
      Logger.debug(
        "No changes in scheduled messages for client #{state.client_id}, keeping existing timers"
      )

      {:reply, :ok, state}
    end
  end

  @impl true
  def handle_info(:connect, state) do
    Logger.debug("Connecting to MQTT broker for client #{inspect(state)}")

    case do_connect(state.connection, state.broker) do
      {:ok, client_pid, mqtt_opts} ->
        # Monitor the client process
        Process.monitor(client_pid)

        # Update state
        new_state = %{
          state
          | mqtt_client_pid: client_pid,
            mqtt_opts: mqtt_opts,
            status: @status_connected
        }

        # Store client info in ETS table
        EtsOperations.store_client_record(
          state.broker.name,
          state.client_id,
          client_pid,
          mqtt_opts,
          @status_connected
        )

        # Broadcast status change
        broadcast_status_change(state.broker.name, state.client_id, @status_connected)

        # Resubscribe to saved topics
        resubscribe_to_saved_topics(state.broker.name, state.connection, client_pid)

        # Start scheduled messages
        new_state_with_scheduled = start_saved_scheduled_messages(new_state)

        {:noreply, new_state_with_scheduled}

      {:error, reason, error_message} ->
        Logger.error(
          "Failed to connect MQTT client #{state.client_id}: #{inspect(reason)} - #{error_message}"
        )

        # Create a trace message for the connection error instead of flash message
        Mqttable.MqttPacketProcessor.create_connection_error_trace(
          state.broker.name,
          state.client_id,
          reason,
          error_message
        )

        # Update status to reconnecting in ETS table
        EtsOperations.store_client_record(
          state.broker.name,
          state.client_id,
          nil,
          nil,
          @status_reconnecting
        )

        # Broadcast status change
        broadcast_status_change(state.broker.name, state.client_id, @status_reconnecting)

        # Schedule reconnection
        cancel_reconnect_timer(state)
        timer_ref = Process.send_after(self(), :connect, 10_000)

        new_state = %{
          state
          | status: @status_reconnecting,
            reconnect_timer: timer_ref
        }

        {:noreply, new_state}
    end
  end

  @impl true
  def handle_info({:DOWN, _ref, :process, pid, reason}, state) do
    if pid == state.mqtt_client_pid do
      Logger.warning("MQTT client #{state.client_id} went down: #{inspect(reason)}")

      # Cancel all scheduled message timers since we're disconnected
      cancel_scheduled_message_timers(state)

      # Update client record to remove the pid but keep the client_id for reconnection
      EtsOperations.store_client_record(
        state.broker.name,
        state.client_id,
        nil,
        nil,
        @status_reconnecting
      )

      # Broadcast status change
      broadcast_status_change(state.broker.name, state.client_id, @status_reconnecting)

      # Schedule reconnection
      cancel_reconnect_timer(state)
      timer_ref = Process.send_after(self(), :connect, 5_000)

      new_state = %{
        state
        | mqtt_client_pid: nil,
          status: @status_reconnecting,
          reconnect_timer: timer_ref,
          scheduled_message_timers: %{}
      }

      {:noreply, new_state}
    else
      {:noreply, state}
    end
  end

  @impl true
  def handle_info({:EXIT, pid, reason}, state) do
    if pid == state.mqtt_client_pid do
      Logger.warning("MQTT client #{state.client_id} exited: #{inspect(reason)}")

      # Cancel all scheduled message timers since we're disconnected
      cancel_scheduled_message_timers(state)

      # Update client record to remove the pid but keep the client_id for reconnection
      EtsOperations.store_client_record(
        state.broker.name,
        state.client_id,
        nil,
        nil,
        @status_reconnecting
      )

      # Broadcast status change
      broadcast_status_change(state.broker.name, state.client_id, @status_reconnecting)

      # Schedule reconnection
      cancel_reconnect_timer(state)
      timer_ref = Process.send_after(self(), :connect, 5_000)

      new_state = %{
        state
        | mqtt_client_pid: nil,
          status: @status_reconnecting,
          reconnect_timer: timer_ref,
          scheduled_message_timers: %{}
      }

      {:noreply, new_state}
    else
      {:noreply, state}
    end
  end

  @impl true
  def handle_info({:send_scheduled_message, index, scheduled_message}, state) do
    # Send the scheduled message if client is connected
    case state.mqtt_client_pid do
      nil ->
        Logger.debug(
          "Skipping scheduled message #{index} for client #{state.client_id}: not connected"
        )

      client_pid ->
        # Send the message
        topic = get_scheduled_message_topic(scheduled_message)
        payload = get_scheduled_message_payload(scheduled_message, state.broker)
        qos = get_scheduled_message_qos(scheduled_message)
        retain = get_scheduled_message_retain(scheduled_message)

        # Build MQTT 5.0 properties if available
        properties = build_scheduled_message_properties(scheduled_message, state.connection)

        publish_opts = [
          qos: qos,
          retain: retain

        try do
          case :mqtt_client.publish(client_pid, topic, properties, payload, publish_opts) do
            {:ok, packet_id} when is_map(packet_id) ->
              # Check reason code for QoS 1/2 messages
              case Map.get(packet_id, :reason_code, 0) do
                0 ->
                  # Success
                  Logger.debug(
                    "Sent scheduled message #{index} for client #{state.client_id}: #{topic} #{inspect(packet_id)}"
                  )

                reason_code ->
                  # Failure - reason code indicates error
                  reason_name = Map.get(packet_id, :reason_code_name, "unknown")

                  Logger.warning(
                    "Failed to send scheduled message #{index} for client #{state.client_id}: #{topic} - #{reason_name} (code: #{reason_code})"
                  )
              end

            :ok ->
              :ok

            {:error, reason} ->
              Logger.warning(
                "Failed to send scheduled message #{index} for client #{state.client_id}: #{inspect(reason)}"
              )
          end
        catch
          :exit, reason ->
            Logger.warning(
              "Failed to send scheduled message #{index} for client #{state.client_id}: #{inspect(reason)}"
            )
        end
    end

    # Reschedule the next message if timer still exists
    case Map.get(state.scheduled_message_timers, index) do
      nil ->
        # Timer was cancelled, don't reschedule
        {:noreply, state}

      old_timer_ref ->
        # Explicitly cancel the old timer reference (though it should already be consumed)
        Process.cancel_timer(old_timer_ref)

        # Schedule next message
        interval_ms = get_scheduled_message_interval(scheduled_message)

        new_timer_ref =
          Process.send_after(
            {:send_scheduled_message, index, scheduled_message},
            interval_ms
          )

        updated_timers = Map.put(state.scheduled_message_timers, index, new_timer_ref)
        new_state = %{state | scheduled_message_timers: updated_timers}
        {:noreply, new_state}
    end
  end

  @impl true
  def handle_info({:publish, _}, state) do
    {:noreply, state}
  end

  @impl true
  def handle_info(msg, state) do
    Logger.info("Received unexpected message in MqttClient.Worker: #{inspect(msg)}")
    {:noreply, state}
  end

  @impl true
  def terminate(_reason, state) do
    cancel_reconnect_timer(state)
    cancel_scheduled_message_timers(state)

    # Disconnect MQTT client if connected
    if state.mqtt_client_pid do
      do_disconnect(state.mqtt_client_pid)
    end

    # Remove from ETS table
    EtsOperations.remove_client_record(state.broker.name, state.client_id)

    # Broadcast disconnected status
    broadcast_status_change(state.broker.name, state.client_id, @status_disconnected)

    :ok
  end

  defp cancel_reconnect_timer(state) do
    if state.reconnect_timer do
      Process.cancel_timer(state.reconnect_timer)
    end
  end

  # Private helper functions

  defp via_tuple(broker_name, client_id) do
    {:via, Registry, {Mqttable.MqttClient.Registry, {broker_name, client_id}}}
  end

  defp get_worker_pid(broker_name, client_id) do
    case Registry.lookup(Mqttable.MqttClient.Registry, {broker_name, client_id}) do
      [{pid, _}] -> {:ok, pid}
      [] -> {:error, :not_found}
    end
  end

  # MQTT connection functions

  defp do_connect(connection, broker) do
    # Build MQTT options using the ConnectionBuilder
    mqtt_opts = ConnectionBuilder.build_mqtt_options(connection, broker)

    # Start MQTT client
    Logger.debug("mqtt_opts: #{inspect(mqtt_opts)}")
    Logger.debug("broker: #{inspect(broker)}")

    case :mqtt_client.start_link(mqtt_opts) do
      {:ok, client_pid} ->
        connect_to_broker(client_pid, broker, mqtt_opts)

      {:error, reason} ->
        error_message = ErrorHandler.format_mqtt_error(reason)
        Logger.error("Failed to start MQTT client: #{inspect(reason)} - #{error_message}")
        {:error, reason, error_message}
    end
  end

  # Connect to broker with the client
  defp connect_to_broker(client_pid, broker, mqtt_opts) do
    try do
      # Use appropriate connection method based on protocol
      connect_result =
        case broker.protocol do
          protocol when protocol in ["ws", "wss"] ->
            :mqtt_client.ws_connect(client_pid)

          _ ->
            :mqtt_client.connect(client_pid)
        end

      case connect_result do
        {:ok, _props} ->
          Logger.info(
            "Connected to MQTT broker: #{broker.host}:#{broker.port} via #{broker.protocol}"
          )

          {:ok, client_pid, mqtt_opts}

        {:error, reason} ->
          handle_connect_error(client_pid, reason)
      end
    catch
      :exit, {:shutdown, :tcp_closed} ->
        handle_connect_exit(client_pid, :tcp_closed)

      :exit, {:socket_closed_before_connack, reason} ->
        handle_connect_exit(client_pid, {:socket_closed_before_connack, reason})

      :exit, reason ->
        handle_connect_exit(client_pid, reason)
    end
  end

  # Handle connection error
  defp handle_connect_error(client_pid, reason) do
    error_message = ErrorHandler.format_mqtt_error(reason)

    Logger.error("Failed to connect to MQTT broker: #{inspect(reason)} - #{error_message}")
    safe_stop(client_pid)
    {:error, reason, error_message}
  end

  # Handle connection exit
  defp handle_connect_exit(client_pid, {:shutdown, :tcp_closed}) do
    error_message = ErrorHandler.format_mqtt_error({:shutdown, :tcp_closed})
    Logger.error("Failed to connect to MQTT broker: tcp_closed - #{error_message}")
    safe_stop(client_pid)
    {:error, :tcp_closed, error_message}
  end

  defp handle_connect_exit(client_pid, {:socket_closed_before_connack, reason}) do
    error_message = ErrorHandler.format_mqtt_error({:socket_closed_before_connack, reason})

    Logger.error(
      "Failed to connect to MQTT broker: socket_closed_before_connack - #{error_message}"
    )

    safe_stop(client_pid)
    {:error, :socket_closed_before_connack, error_message}
  end

  defp handle_connect_exit(client_pid, reason) do
    error_message = ErrorHandler.extract_concise_error_message(reason)

    Logger.error("Failed to connect to MQTT broker with unexpected exit: #{error_message}")
    safe_stop(client_pid)
    {:error, reason, error_message}
  end

  # MQTT disconnection functions

  defp do_disconnect(client_pid) do
    try do
      disconnect_if_alive(client_pid)
    rescue
      e ->
        Logger.error("Error disconnecting MQTT client: #{inspect(e)}")
        :ok
    catch
      :exit, reason ->
        Logger.error("Exit while disconnecting MQTT client: #{inspect(reason)}")
        :ok
    end
  end

  defp disconnect_if_alive(client_pid) do
    if Process.alive?(client_pid) do
      # Try to disconnect gracefully first
      _ = safe_disconnect(client_pid)

      # Try to stop the process
      _ = safe_stop(client_pid)

      :ok
    else
      # Process is already dead, so consider it successfully disconnected
      Logger.info("MQTT client process is already terminated")
      :ok
    end
  end

  defp safe_disconnect(client_pid) do
    try do
      :mqtt_client.disconnect(client_pid)
    catch
      :exit, _ ->
        :ok
    end
  end

  defp safe_stop(client_pid) do
    if Process.alive?(client_pid) do
      try do
        :mqtt_client.stop(client_pid)
      rescue
        _error ->
          :ok
      catch
        :exit, {:noproc, _} ->
          :ok

        :exit, reason ->
          Logger.debug(
            "Exit while stopping MQTT client #{inspect(client_pid)}: #{inspect(reason)}"
          )

          :ok
      end
    else
      Logger.debug("MQTT client process #{inspect(client_pid)} is already dead")
      :ok
    end
  end

  # PubSub functions

  defp broadcast_status_change(broker_name, client_id, status) do
    PubSub.broadcast(
      Mqttable.PubSub,
      @pubsub_topic,
      {:mqtt_client_status_changed, broker_name, client_id, status}
    )

    # If the status is connected, also broadcast the connection time
    if status == @status_connected do
      PubSub.broadcast(
        Mqttable.PubSub,
        @pubsub_topic,
        {:mqtt_client_connection_time, client_id, DateTime.utc_now()}
      )
    end
  end

  defp broadcast_topic_subscription(broker_name, client_id, topic, opts, sub_id) do
    PubSub.broadcast(
      Mqttable.PubSub,
      @pubsub_topic,
      {:mqtt_client_topic_subscribed, broker_name, client_id, topic, opts, sub_id}
    )
  end

  defp broadcast_topic_unsubscription(broker_name, client_id, topic) do
    PubSub.broadcast(
      Mqttable.PubSub,
      @pubsub_topic,
      {:mqtt_client_topic_unsubscribed, broker_name, client_id, topic}
    )
  end

  defp broadcast_subscription_failure(broker_name, client_id, topic, error_message) do
    PubSub.broadcast(
      Mqttable.PubSub,
      @pubsub_topic,
      {:mqtt_client_subscription_failed, broker_name, client_id, topic, error_message}
    )
  end

  # Resubscribe to saved topics for a reconnected client
  defp resubscribe_to_saved_topics(broker_name, connection, client_pid) do
    client_id = connection.client_id
    topics = Map.get(connection, :topics, [])

    if topics != [] do
      Logger.info("Resubscribing client #{client_id} to #{length(topics)} saved topics")

      # Process topics using SubscriptionManager
      topics
      |> SubscriptionManager.process_saved_topics_for_resubscription()
      |> Enum.each(fn {topic, {sub_opts, props}} ->
        try_resubscribe(broker_name, client_pid, client_id, topic, {sub_opts, props})
      end)
    end
  end

  # Try to resubscribe to a topic
  defp try_resubscribe(broker_name, client_pid, client_id, topic, {sub_opts, props}) do
    case SubscriptionManager.prepare_topic_for_subscription(topic, sub_opts, props) do
      {:ok, {validated_topic, processed_opts, validated_props}} ->
        try do
          case :mqtt_client.subscribe(client_pid, validated_props, [
                 {validated_topic, processed_opts}
               ]) do
            {:ok, _props, reason_codes} ->
              case SubscriptionManager.check_subscription_success(reason_codes) do
                :ok ->
                  SubscriptionManager.log_resubscription_attempt(client_id, topic, :ok)
                  :ok

                {:error, error_message} ->
                  Logger.error(
                    "Failed to resubscribe client #{client_id} to topic #{topic}: #{error_message}"
                  )

                  # Broadcast subscription failure and remove from connection state
                  broadcast_subscription_failure(broker_name, client_id, topic, error_message)
                  :error
              end

            {:error, reason} ->
              error_message = ErrorHandler.format_mqtt_error(reason)

              Logger.error(
                "Failed to resubscribe client #{client_id} to topic #{topic}: #{error_message}"
              )

              # Broadcast subscription failure and remove from connection state
              broadcast_subscription_failure(broker_name, client_id, topic, error_message)
              :error
          end
        catch
          :exit, reason ->
            error_message =
              "Exit while resubscribing client #{client_id} to topic #{topic}: #{inspect(reason)}"

            Logger.error(error_message)

            # Broadcast subscription failure and remove from connection state
            broadcast_subscription_failure(broker_name, client_id, topic, error_message)
            :error
        end

      {:error, error_message} ->
        SubscriptionManager.log_topic_validation_failure(topic, error_message)

        # Broadcast subscription failure and remove from connection state
        broadcast_subscription_failure(broker_name, client_id, topic, error_message)
        :error
    end
  end

  # Start saved scheduled messages for a reconnected client
  defp start_saved_scheduled_messages(state) do
    scheduled_messages = Map.get(state.connection, :scheduled_messages, [])

    if scheduled_messages != [] do
      Logger.info(
        "Starting #{length(scheduled_messages)} scheduled messages for client #{state.client_id}"
      )

      # Use the same logic as sync_scheduled_messages but without the GenServer call
      # Stop all existing timers (should be empty after reconnection since we cancel them on disconnect)
      Enum.each(state.scheduled_message_timers, fn {_index, timer_ref} ->
        Process.cancel_timer(timer_ref)
      end)

      # Start new timers for the scheduled messages
      updated_timers =
        scheduled_messages
        |> Enum.with_index()
        |> Enum.reduce(%{}, fn {scheduled_message, index}, acc ->
          interval_ms = get_scheduled_message_interval(scheduled_message)

          timer_ref =
            Process.send_after(
              {:send_scheduled_message, index, scheduled_message},
              interval_ms
            )

          Map.put(acc, index, timer_ref)
        end)

      %{state | scheduled_message_timers: updated_timers}
    else
      state
    end
  end

  # Cancel all scheduled message timers
  defp cancel_scheduled_message_timers(state) do
    Enum.each(state.scheduled_message_timers, fn {_index, timer_ref} ->
      Process.cancel_timer(timer_ref)
    end)
  end

  # Helper functions to safely get properties from scheduled message (handles both atom and string keys)
  defp get_scheduled_message_interval(scheduled_message) when is_map(scheduled_message) do
    Map.get(scheduled_message, :interval_ms, Map.get(scheduled_message, "interval_ms", 5000))
  end

  defp get_scheduled_message_interval(_), do: 5000

  defp get_scheduled_message_topic(scheduled_message) when is_map(scheduled_message) do
    Map.get(scheduled_message, :topic, Map.get(scheduled_message, "topic", "unknown"))
  end

  defp get_scheduled_message_topic(_), do: "unknown"

  defp get_scheduled_message_payload(scheduled_message, broker) when is_map(scheduled_message) do
    payload_format =
      Map.get(
        scheduled_message,
        :payload_format,
        Map.get(scheduled_message, "payload_format", "text")
      )

    # Get payload from format-specific fields instead of main payload field
    payload =
      get_current_payload_for_format_from_scheduled_message(scheduled_message, payload_format)

    file_encoding =
      Map.get(
        scheduled_message,
        :file_encoding,
        Map.get(scheduled_message, "file_encoding", "binary")
      )

    # First, handle template rendering if needed
    processed_payload =
      if String.contains?(payload, "{{") || String.contains?(payload, "{%") do
        # Get broker variables if broker is provided
        variables = get_broker_variables_from_broker(broker)

        case Mqttable.Templating.Engine.render(payload, %{}, variables) do
          {:ok, rendered_payload} ->
            rendered_payload

          {:error, reason} ->
            Logger.warning(
              "Template rendering failed for scheduled message: #{reason}, using original payload"
            )

            payload
        end
      else
        payload
      end

    # Then, handle payload encoding based on format
    case payload_format do
      "file" ->
        # For file format, the payload contains the file path, not the content
        # We need to read the file content from storage
        case FileStorage.read_file(processed_payload) do
          {:ok, file_content} ->
            encode_file_content(file_content, file_encoding)

          {:error, error_message} ->
            Logger.warning(
              "Failed to read file for scheduled message: #{error_message}, using empty payload"
            )

            ""
        end

      "hex" ->
        # Handle hex payload decoding
        cleaned = String.replace(processed_payload, ~r/\s/, "")

        case Base.decode16(cleaned, case: :mixed) do
          {:ok, binary} ->
            binary

          :error ->
            Logger.warning(
              "Failed to decode hex payload for scheduled message, using original payload"
            )

            processed_payload
        end

      _ ->
        # For text, json, and other formats, use as-is
        processed_payload
    end
  end

  defp get_scheduled_message_payload(_, _), do: ""

  defp get_scheduled_message_qos(scheduled_message) when is_map(scheduled_message) do
    Map.get(scheduled_message, :qos, Map.get(scheduled_message, "qos", 0))
  end

  defp get_scheduled_message_qos(_), do: 0

  defp get_scheduled_message_retain(scheduled_message) when is_map(scheduled_message) do
    Map.get(scheduled_message, :retain, Map.get(scheduled_message, "retain", false))
  end

  defp get_scheduled_message_retain(_), do: false

  # Helper function to build MQTT 5.0 properties for scheduled messages
  defp build_scheduled_message_properties(scheduled_message, connection)
       when is_map(scheduled_message) do
    # Only build properties if connection supports MQTT 5.0
    case Map.get(connection, :mqtt_version) do
      "5.0" ->
        properties = %{}

        # Add content type if provided
        properties =
          case get_scheduled_msg_property(scheduled_message, :content_type, "") do
            "" -> properties
            content_type -> Map.put(properties, :"Content-Type", content_type)
          end

        # Add payload format indicator
        properties =
          if get_scheduled_msg_property(scheduled_message, :payload_format_indicator, false) do
            Map.put(properties, :"Payload-Format-Indicator", 1)
          else
            properties
          end

        # Add message expiry interval
        properties =
          case get_scheduled_msg_property(scheduled_message, :message_expiry_interval, 0) do
            0 -> properties
            expiry -> Map.put(properties, :"Message-Expiry-Interval", expiry)
          end

        # Add topic alias
        properties =
          case get_scheduled_msg_property(scheduled_message, :topic_alias, 0) do
            0 -> properties
            alias -> Map.put(properties, :"Topic-Alias", alias)
          end

        # Add response topic
        properties =
          case get_scheduled_msg_property(scheduled_message, :response_topic, "") do
            "" -> properties
            response_topic -> Map.put(properties, :"Response-Topic", response_topic)
          end

        # Add correlation data
        properties =
          case get_scheduled_msg_property(scheduled_message, :correlation_data, "") do
            "" -> properties
            correlation_data -> Map.put(properties, :"Correlation-Data", correlation_data)
          end

        # Add user properties
        user_properties = get_scheduled_msg_property(scheduled_message, :user_properties, [])

        valid_user_properties =
          Enum.filter(user_properties, fn
            %{"key" => key, "value" => value} when is_binary(key) and is_binary(value) ->
              key != "" && value != ""

            _ ->
              false
          end)

        properties =
          if length(valid_user_properties) > 0 do
            add_user_properties_to_map(properties, valid_user_properties)
          else
            properties
          end

        properties

      _ ->
        # Client doesn't support MQTT 5.0
        %{}
    end
  end

  defp build_scheduled_message_properties(_, _), do: %{}

  # Helper function to safely get properties from scheduled message (handles both atom and string keys)
  defp get_scheduled_msg_property(scheduled_msg, property, default) when is_map(scheduled_msg) do
    Map.get(scheduled_msg, property, Map.get(scheduled_msg, to_string(property), default))
  end

  defp get_scheduled_msg_property(_, _, default), do: default

  defp get_broker_variables_from_broker(broker) when is_map(broker) do
    # Extract enabled variables from broker and convert to map
    broker
    |> Map.get(:variables, [])
    |> Enum.filter(fn var -> Map.get(var, :enabled, true) end)
    |> Enum.reduce(%{}, fn var, acc ->
      name = Map.get(var, :name)
      value = Map.get(var, :value, "")

      if name && name != "" do
        Map.put(acc, name, value)
      else
        acc
      end
    end)
  end

  defp get_broker_variables_from_broker(_broker) do
    %{}
  end

  # Helper function to get current payload from format-specific fields in scheduled message
  defp get_current_payload_for_format_from_scheduled_message(scheduled_message, format) do
    case format do
      "text" ->
        Map.get(scheduled_message, :payload_text, Map.get(scheduled_message, "payload_text", ""))

      "json" ->
        Map.get(scheduled_message, :payload_json, Map.get(scheduled_message, "payload_json", ""))

      "hex" ->
        Map.get(scheduled_message, :payload_hex, Map.get(scheduled_message, "payload_hex", ""))

      "file" ->
        Map.get(scheduled_message, :payload_file, Map.get(scheduled_message, "payload_file", ""))

      _ ->
        Map.get(scheduled_message, :payload_text, Map.get(scheduled_message, "payload_text", ""))
    end
  end

  defp encode_file_content(file_content, file_encoding) do
    case file_encoding do
      "base64" ->
        # Send file as base64-encoded text
        Base.encode64(file_content)

      _ ->
        # Default: send raw binary content
        file_content
    end
  end

  defp add_user_properties_to_map(properties, valid_user_properties) do
    user_props_list =
      Enum.map(valid_user_properties, fn %{"key" => key, "value" => value} ->
        {key, value}
      end)

    Map.put(properties, :"User-Property", user_props_list)
  end
end
