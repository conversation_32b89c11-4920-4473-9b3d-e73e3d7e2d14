#!/usr/bin/env elixir

defmodule SpecRemover do
  @moduledoc """
  Script to remove all @spec declarations from Elixir (.ex) files.
  """

  def run do
    IO.puts("Starting to remove @spec declarations from .ex files...")

    # Find all .ex files in the project
    ex_files = find_ex_files(".")

    IO.puts("Found #{length(ex_files)} .ex files")

    # Process each file
    Enum.each(ex_files, &process_file/1)

    IO.puts("Finished removing @spec declarations!")
  end

  defp find_ex_files(directory) do
    Path.wildcard("#{directory}/**/*.ex")
    |> Enum.filter(&File.regular?/1)
  end

  defp process_file(file_path) do
    IO.puts("Processing: #{file_path}")

    case File.read(file_path) do
      {:ok, content} ->
        new_content = remove_specs(content)

        if content != new_content do
          File.write!(file_path, new_content)
          IO.puts("  ✓ Updated #{file_path}")
        else
          IO.puts("  - No changes needed for #{file_path}")
        end

      {:error, reason} ->
        IO.puts("  ✗ Error reading #{file_path}: #{reason}")
    end
  end

  defp remove_specs(content) do
    # Use regex to remove complete @spec blocks including multiline ones
    content
    |> remove_complete_spec_blocks()
    |> remove_orphaned_spec_lines()
  end

  defp remove_complete_spec_blocks(content) do
    # Remove complete @spec blocks that start with @spec and end with ::
    Regex.replace(
      ~r/@spec\s+[^@]*?::[^@\n]*(?:\n\s*when[^@\n]*)*(?:\n|$)/s,
      content,
      ""
    )
  end

  defp remove_orphaned_spec_lines(content) do
    content
    |> String.split("\n")
    |> Enum.reject(&orphaned_spec_line?/1)
    |> Enum.join("\n")
  end

  defp orphaned_spec_line?(line) do
    trimmed = String.trim(line)

    # Remove lines that look like orphaned spec parts
    cond do
      # Lines that start with @spec
      String.starts_with?(trimmed, "@spec ") ->
        true

      # Lines that are clearly spec continuations (indented and contain type syntax)
      String.match?(line, ~r/^\s+.*(::|->|\||,)\s*$/) and
          String.match?(line, ~r/^\s+[a-zA-Z_][a-zA-Z0-9_]*\(\)/) ->
        true

      # Lines that look like type definitions in specs
      String.match?(line, ~r/^\s+[a-zA-Z_][a-zA-Z0-9_]*\(\)\s*,?\s*$/) ->
        true

      # Lines with just type syntax
      String.match?(line, ~r/^\s*\)\s*::\s*[a-zA-Z_]/) ->
        true

      # Lines that look like standalone type unions (e.g., "| {:error, String.t()}")
      String.match?(line, ~r/^\s*\|\s*\{.*\}\s*$/) ->
        true

      # Lines that look like type definitions with parentheses and unions
      String.match?(line, ~r/^\s*\{.*\(\)\s*.*\}\s*\|\s*\{.*\}\s*$/) ->
        true

      # Lines that are just closing brackets or parentheses from type specs
      String.match?(line, ~r/^\s*\]\s*$/) and String.length(trimmed) < 10 ->
        true

      true ->
        false
    end
  end
end

# Run the script
SpecRemover.run()

